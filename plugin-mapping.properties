#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# This mapping is used to resolve the Jar package name without version (or call artifactId)
# corresponding to the module in the user Config, helping SeaTunnel to load the correct Jar package.
## *** WARNING **** : `seatunnel.source.XXX`, the `XXX` should be string which SeaTunnelSource::getPluginName and TableSinkFactory::factoryIdentifier returned value##
# SeaTunnel Connector-V2
seatunnel.source.FakeSource=connector-fake
seatunnel.sink.Console=connector-console
seatunnel.sink.ConsoleHole=connector-console-hole
seatunnel.sink.Assert=connector-assert
seatunnel.source.Kafka=connector-kafka
seatunnel.sink.Kafka=connector-kafka
seatunnel.source.Http=connector-http-base
seatunnel.sink.Http=connector-http-base
seatunnel.sink.Feishu=connector-http-feishu
seatunnel.source.Socket=connector-socket
seatunnel.sink.Hive=connector-hive
seatunnel.source.Hive=connector-hive
seatunnel.source.Clickhouse=connector-clickhouse
seatunnel.sink.Clickhouse=connector-clickhouse
seatunnel.sink.ClickhouseFile=connector-clickhouse
seatunnel.source.Jdbc=connector-jdbc
seatunnel.sink.Jdbc=connector-jdbc
seatunnel.source.Kudu=connector-kudu
seatunnel.sink.Kudu=connector-kudu
seatunnel.sink.EmailSink=connector-email
seatunnel.source.HdfsFile=connector-file-hadoop
seatunnel.sink.HdfsFile=connector-file-hadoop
seatunnel.source.LocalFile=connector-file-local
seatunnel.sink.LocalFile=connector-file-local
seatunnel.source.OssFile=connector-file-oss
seatunnel.sink.OssFile=connector-file-oss
seatunnel.source.OssJindoFile=connector-file-jindo-oss
seatunnel.sink.OssJindoFile=connector-file-jindo-oss
seatunnel.source.CosFile=connector-file-cos
seatunnel.sink.CosFile=connector-file-cos
seatunnel.source.Pulsar=connector-pulsar
seatunnel.source.Hudi=connector-hudi
seatunnel.sink.DingTalk=connector-dingtalk
seatunnel.source.Elasticsearch=connector-elasticsearch
seatunnel.sink.Elasticsearch=connector-elasticsearch
seatunnel.source.IoTDB=connector-iotdb
seatunnel.sink.IoTDB=connector-iotdb
seatunnel.source.Neo4j=connector-neo4j
seatunnel.sink.Neo4j=connector-neo4j
seatunnel.source.FtpFile=connector-file-ftp
seatunnel.sink.FtpFile=connector-file-ftp
seatunnel.source.SftpFile=connector-file-sftp
seatunnel.sink.SftpFile=connector-file-sftp
seatunnel.sink.Socket=connector-socket
seatunnel.source.Redis=connector-redis
seatunnel.sink.Redis=connector-redis
seatunnel.source.DataHub=connector-datahub
seatunnel.sink.DataHub=connector-datahub
seatunnel.sink.Sentry=connector-sentry
seatunnel.source.MongoDB=connector-mongodb
seatunnel.sink.MongoDB=connector-mongodb
seatunnel.source.Iceberg=connector-iceberg
seatunnel.sink.Iceberg=connector-iceberg
seatunnel.source.InfluxDB=connector-influxdb
seatunnel.source.S3File=connector-file-s3
seatunnel.sink.S3File=connector-file-s3
seatunnel.sink.S3IcfsdosFile=connector-file-icfsdos
seatunnel.source.AmazonDynamodb=connector-amazondynamodb
seatunnel.sink.AmazonDynamodb=connector-amazondynamodb
seatunnel.source.Cassandra=connector-cassandra
seatunnel.sink.Cassandra=connector-cassandra
seatunnel.sink.StarRocks=connector-starrocks
seatunnel.source.MyHours=connector-http-myhours
seatunnel.sink.InfluxDB=connector-influxdb
seatunnel.source.GoogleSheets=connector-google-sheets
seatunnel.sink.GoogleFirestore=connector-google-firestore
seatunnel.sink.Tablestore=connector-tablestore
seatunnel.source.Lemlist=connector-http-lemlist
seatunnel.source.Klaviyo=connector-http-klaviyo
seatunnel.sink.Slack=connector-slack
seatunnel.source.OneSignal=connector-http-onesignal
seatunnel.source.Jira=connector-http-jira
seatunnel.source.Gitlab=connector-http-gitlab
seatunnel.source.Github=connector-http-github
seatunnel.source.Notion=connector-http-notion
seatunnel.sink.RabbitMQ=connector-rabbitmq
seatunnel.source.RabbitMQ=connector-rabbitmq
seatunnel.source.OpenMldb=connector-openmldb
seatunnel.source.SqlServer-CDC=connector-cdc-sqlserver
seatunnel.source.Doris=connector-doris
seatunnel.sink.Doris=connector-doris
seatunnel.source.Maxcompute=connector-maxcompute
seatunnel.sink.Maxcompute=connector-maxcompute
seatunnel.source.MySQL-CDC=connector-cdc-mysql
seatunnel.source.MongoDB-CDC=connector-cdc-mongodb
seatunnel.sink.S3Redshift=connector-s3-redshift
seatunnel.source.TDengine=connector-tdengine
seatunnel.sink.TDengine=connector-tdengine
seatunnel.source.Persistiq=connector-http-persistiq
seatunnel.sink.SelectDBCloud=connector-selectdb-cloud
seatunnel.sink.Hbase=connector-hbase
seatunnel.source.Hbase = connector-hbase
seatunnel.source.StarRocks=connector-starrocks
seatunnel.source.Rocketmq=connector-rocketmq
seatunnel.sink.Rocketmq=connector-rocketmq
seatunnel.source.AmazonSqs=connector-amazonsqs
seatunnel.sink.AmazonSqs=connector-amazonsqs
seatunnel.source.Paimon=connector-paimon
seatunnel.sink.Paimon=connector-paimon
seatunnel.source.Postgres-CDC=connector-cdc-postgres
seatunnel.source.Oracle-CDC=connector-cdc-oracle
seatunnel.sink.Pulsar=connector-pulsar
seatunnel.source.ArgoHdfsFile=connector-hive-argofile-hadoop
seatunnel.sink.ArgoHdfsFile=connector-hive-argofile-hadoop
seatunnel.source.ArgoLocalFile=connector-hive-argofile-local
seatunnel.sink.ArgoLocalFile=connector-hive-argofile-local
seatunnel.source.ObsFile=connector-file-obs
seatunnel.sink.ObsFile=connector-file-obs
seatunnel.sink.MergeFtpFile=connector-file-mergefile-ftp
seatunnel.sink.MergeHdfsFile=connector-file-mergefile-hadoop
seatunnel.sink.MergeLocalFile=connector-file-mergefile-local
seatunnel.sink.MergeObsFile=connector-file-mergefile-obs
seatunnel.sink.MergeOssFile=connector-file-mergefile-oss
seatunnel.sink.MergeS3File=connector-file-mergefile-s3
seatunnel.sink.MergeSftpFile=connector-file-mergefile-sftp
seatunnel.source.ArgoAdbHdfsFile=connector-hive-adbargo-hadoop
seatunnel.sink.ArgoAdbHdfsFile=connector-hive-adbargo-hadoop
seatunnel.source.ArgoAdbLocalFile=connector-hive-adbargo-local
seatunnel.sink.ArgoAdbLocalFile=connector-hive-adbargo-local
seatunnel.sink.Gds2DwsFile=connector-file-gds2dws-local
seatunnel.sink.Prometheus=connector-prometheus
seatunnel.source.Prometheus = connector-prometheus
seatunnel.sink.Sls=connector-sls
seatunnel.source.Sls = connector-sls