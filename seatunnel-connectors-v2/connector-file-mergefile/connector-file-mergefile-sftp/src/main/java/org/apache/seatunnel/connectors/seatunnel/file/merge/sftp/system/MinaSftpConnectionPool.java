/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.merge.sftp.system;

import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.client.SftpClientFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/** High-performance SFTP connection pool using Apache MINA SSHD 彻底重构的连接池，解决所有高并发问题 */
public class MinaSftpConnectionPool {

    public static final Logger LOG = LoggerFactory.getLogger(MinaSftpConnectionPool.class);

    // 最大连接数
    private final int maxConnection;
    // 活跃连接计数
    private final AtomicInteger activeConnectionCount = new AtomicInteger(0);

    // 空闲连接池：ConnectionInfo -> BlockingQueue<MinaSftpConnection>
    private final ConcurrentHashMap<ConnectionInfo, BlockingQueue<MinaSftpConnection>>
            idleConnections = new ConcurrentHashMap<>();

    // 连接信息映射：MinaSftpConnection -> ConnectionInfo
    private final ConcurrentHashMap<MinaSftpConnection, ConnectionInfo> connectionInfoMap =
            new ConcurrentHashMap<>();

    // SSH客户端
    private final SshClient sshClient;

    public MinaSftpConnectionPool(int maxConnection, int initialConnectionCount) {
        this.maxConnection = Math.max(maxConnection, 100);
        this.activeConnectionCount.set(0); // 初始时没有活跃连接

        // 创建SSH客户端
        this.sshClient = SshClient.setUpDefaultClient();
        this.sshClient.getProperties().put("MaxAuthTries", "6");
        this.sshClient.getProperties().put("ServerAliveInterval", "30");
        this.sshClient.getProperties().put("ServerAliveCountMax", "3");
        this.sshClient.start();

        LOG.info("MinaSftpConnectionPool initialized with maxConnections={}", this.maxConnection);
    }

    /** 获取连接 */
    public synchronized MinaSftpConnection connect(
            String host, int port, String user, String password, String keyFile)
            throws IOException {
        ConnectionInfo info = new ConnectionInfo(host, port, user);

        // 1. 首先尝试从空闲池获取连接
        MinaSftpConnection connection = getFromIdlePool(info);
        if (connection != null) {
            // 连接从空闲池获取，标记为活跃
            connectionInfoMap.put(connection, info);
            activeConnectionCount.incrementAndGet();
            LOG.debug("Reused connection from pool for {}", info.getHost());
            return connection;
        }

        // 2. 检查是否可以创建新连接
        if (activeConnectionCount.get() >= maxConnection) {
            // 尝试清理无效连接
            cleanupInvalidConnections();

            if (activeConnectionCount.get() >= maxConnection) {
                throw new IOException(
                        "Connection pool exhausted, max connections: "
                                + maxConnection
                                + ", active: "
                                + activeConnectionCount.get());
            }
        }

        // 3. 创建新连接
        try {
            ClientSession session = sshClient.connect(user, host, port).verify(180000).getSession();

            if (password != null && !password.isEmpty()) {
                session.addPasswordIdentity(password);
            } else {
                session.addPasswordIdentity("");
            }

            session.auth().verify(180000);
            SftpClient sftpClient = SftpClientFactory.instance().createSftpClient(session);
            connection = new MinaSftpConnection(session, sftpClient);

            // 添加到活跃连接映射
            connectionInfoMap.put(connection, info);
            activeConnectionCount.incrementAndGet();

            LOG.debug(
                    "Created new connection for {}. Active: {}, Idle: {}",
                    info.getHost(),
                    activeConnectionCount.get(),
                    getIdleCount());

            return connection;

        } catch (Exception e) {
            throw new IOException("Failed to connect to SFTP server: " + e.getMessage(), e);
        }
    }

    /** 释放连接 */
    public synchronized void disconnect(MinaSftpConnection connection) throws IOException {
        if (connection == null) {
            return;
        }

        ConnectionInfo info = connectionInfoMap.get(connection);
        if (info == null) {
            // 连接不在映射中，直接关闭
            if (connection.isConnected()) {
                connection.close();
            }
            return;
        }

        // 从活跃连接映射中移除
        connectionInfoMap.remove(connection);
        activeConnectionCount.decrementAndGet();

        if (connection.isConnected()) {
            // 连接仍然有效，尝试返回空闲池
            BlockingQueue<MinaSftpConnection> queue = idleConnections.get(info);
            if (queue == null) {
                queue = idleConnections.computeIfAbsent(info, k -> new LinkedBlockingQueue<>());
            }

            // 检查空闲池是否已满
            if (queue.size() >= 10) { // 每个主机的最大空闲连接数
                // 空闲池已满，关闭连接
                connection.close();
                LOG.debug("Idle pool full, closed connection for {}", info.getHost());
            } else {
                // 返回空闲池
                queue.offer(connection);
                LOG.debug("Returned connection to idle pool for {}", info.getHost());
            }
        } else {
            // 连接已断开，直接关闭
            connection.close();
            LOG.debug("Closed disconnected connection for {}", info.getHost());
        }
    }

    /** 从空闲池获取连接 */
    private MinaSftpConnection getFromIdlePool(ConnectionInfo info) {
        BlockingQueue<MinaSftpConnection> queue = idleConnections.get(info);
        if (queue != null) {
            MinaSftpConnection connection = queue.poll();
            if (connection != null && connection.isConnected()) {
                return connection;
            } else if (connection != null) {
                // 连接已断开，从映射中移除
                connectionInfoMap.remove(connection);
                activeConnectionCount.decrementAndGet();
            }
        }
        return null;
    }

    /** 清理无效连接 */
    private void cleanupInvalidConnections() {
        LOG.debug("Starting cleanup of invalid connections...");
        int cleanedCount = 0;

        // 清理空闲池中的无效连接
        for (ConnectionInfo info : idleConnections.keySet()) {
            BlockingQueue<MinaSftpConnection> queue = idleConnections.get(info);
            if (queue != null) {
                BlockingQueue<MinaSftpConnection> validConnections = new LinkedBlockingQueue<>();
                MinaSftpConnection connection;

                while ((connection = queue.poll()) != null) {
                    if (connection.isConnected()) {
                        validConnections.offer(connection);
                    } else {
                        // 连接已断开，关闭并移除
                        connectionInfoMap.remove(connection);
                        activeConnectionCount.decrementAndGet();
                        cleanedCount++;
                        try {
                            connection.close();
                        } catch (IOException e) {
                            LOG.warn("Error closing invalid connection", e);
                        }
                    }
                }

                // 更新队列
                if (!validConnections.isEmpty()) {
                    idleConnections.put(info, validConnections);
                } else {
                    idleConnections.remove(info);
                }
            }
        }

        LOG.info("Cleanup completed, removed {} invalid connections", cleanedCount);
    }

    /** 关闭连接池 */
    public synchronized void shutdown() {
        LOG.info(
                "Shutting down connection pool, active connections: {}",
                activeConnectionCount.get());

        // 关闭所有活跃连接
        Set<MinaSftpConnection> activeConnections = connectionInfoMap.keySet();
        for (MinaSftpConnection connection : activeConnections) {
            try {
                connection.close();
            } catch (IOException e) {
                LOG.warn("Error closing active connection", e);
            }
        }

        // 关闭所有空闲连接
        for (BlockingQueue<MinaSftpConnection> queue : idleConnections.values()) {
            MinaSftpConnection connection;
            while ((connection = queue.poll()) != null) {
                try {
                    connection.close();
                } catch (IOException e) {
                    LOG.warn("Error closing idle connection", e);
                }
            }
        }

        // 清空所有映射
        connectionInfoMap.clear();
        idleConnections.clear();
        activeConnectionCount.set(0);

        // 关闭SSH客户端
        if (sshClient != null && sshClient.isStarted()) {
            try {
                sshClient.stop();
            } catch (Exception e) {
                LOG.warn("Error stopping SSH client", e);
            }
        }

        LOG.info("Connection pool shutdown completed");
    }

    /** 获取统计信息 */
    public int getActiveConnectionCount() {
        return activeConnectionCount.get();
    }

    public int getIdleCount() {
        int total = 0;
        for (BlockingQueue<MinaSftpConnection> queue : idleConnections.values()) {
            total += queue.size();
        }
        return total;
    }

    public int getMaxConnection() {
        return maxConnection;
    }

    /** 连接信息类 */
    static class ConnectionInfo {
        private final String host;
        private final int port;
        private final String user;

        ConnectionInfo(String host, int port, String user) {
            this.host = host;
            this.port = port;
            this.user = user;
        }

        public String getHost() {
            return host;
        }

        public int getPort() {
            return port;
        }

        public String getUser() {
            return user;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof ConnectionInfo)) return false;
            ConnectionInfo other = (ConnectionInfo) obj;
            return port == other.port
                    && host.equalsIgnoreCase(other.host)
                    && user.equalsIgnoreCase(other.user);
        }

        @Override
        public int hashCode() {
            return host.toLowerCase().hashCode() + port + user.toLowerCase().hashCode();
        }
    }

    /** SFTP连接包装类 */
    public static class MinaSftpConnection {
        private final ClientSession session;
        private final SftpClient sftpClient;

        public MinaSftpConnection(ClientSession session, SftpClient sftpClient) {
            this.session = session;
            this.sftpClient = sftpClient;
        }

        public ClientSession getSession() {
            return session;
        }

        public SftpClient getSftpClient() {
            return sftpClient;
        }

        public boolean isConnected() {
            return session != null && session.isAuthenticated() && !session.isClosed();
        }

        public void close() throws IOException {
            try {
                if (sftpClient != null) {
                    sftpClient.close();
                }
            } finally {
                if (session != null && !session.isClosed()) {
                    session.close();
                }
            }
        }
    }
}
