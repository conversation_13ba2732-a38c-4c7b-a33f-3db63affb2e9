package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.informix;

import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.DecimalType;
import org.apache.seatunnel.api.table.type.LocalTimeType;
import org.apache.seatunnel.api.table.type.PrimitiveByteArrayType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialectTypeMapper;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * CREATE TABLE informix.all0920_lihj ( c1 integer NOT NULL, c2 smallint, c3 float, c4 smallfloat,
 * c5 decimal(5,2), c6 decimal(4,2), c7 char(5), c8 varchar(100), c9 nchar(5), c10 nvarchar(30), c11
 * date, c12 datetime year to second, c13 datetime year to fraction(3), c14 datetime year to
 * fraction(5), c17 serial NOT NULL, c18 money, c19 text, c20 byte, c21 clob, c22 boolean, c23
 * json(4096)
 *
 * <p>);
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
public class InformixTyperMapper implements JdbcDialectTypeMapper {
    // BOOLEAN
    private static final String INFORMIX_BOOLEAN = "BOOLEAN";
    private static final String INFORMIX_BYTE = "BYTE";

    // 数值
    private static final String INFORMIX_SMALLINT = "SMALLINT";
    private static final String INFORMIX_INTEGER = "INTEGER";
    private static final String INFORMIX_BIGINT = "BIGINT";
    private static final String INFORMIX_INT = "INT";
    private static final String INFORMIX_SERIAL = "SERIAL";

    // 浮点
    private static final String INFORMIX_FLOAT = "FLOAT";
    private static final String INFORMIX_SMALLFLOAT = "SMALLFLOAT";
    private static final String INFORMIX_REAL = "REAL";
    private static final String INFORMIX_DOUBLE_PRECISION = "DOUBLE PRECISION";
    private static final String INFORMIX_DECIMAL = "DECIMAL";
    private static final String INFORMIX_NUMERIC = "NUMERIC";
    private static final String INFORMIX_MONEY = "MONEY";

    // 字符串
    private static final String INFORMIX_CHAR = "CHAR";
    private static final String INFORMIX_NCHAR = "NCHAR";
    private static final String INFORMIX_NVARCHAR = "NVARCHAR";
    private static final String INFORMIX_CHARACTER = "CHARACTER";
    private static final String INFORMIX_VARCHAR = "VARCHAR";
    private static final String INFORMIX_LVARCHAR = "LVARCHAR";
    private static final String INFORMIX_TEXT = "TEXT";
    private static final String INFORMIX_JSON = "JSON";
    private static final String INFORMIX_INTERVAL_DAY_TO_SECOND = "INTERVAL DAY(2) TO SECOND";
    private static final String INFORMIX_INTERVAL_YEAR_TO_MONTH = "INTERVAL YEAR(4) TO MONTH";

    // 日期
    private static final String INFORMIX_DATE = "DATE";
    private static final String INFORMIX_DATETIME = "DATETIME";
    private static final String INFORMIX_DATETIME_YEAR_TO_SECOND = "DATETIME YEAR TO SECOND";
    private static final String INFORMIX_DATETIME_YEAR_TO_FRACTION = "DATETIME YEAR TO FRACTION(3)";
    private static final String INFORMIX_DATETIME_YEAR_TO_FRACTION5 =
            "DATETIME YEAR TO FRACTION(5)";
    private static final String INFORMIX_INTERVAL = "INTERVAL";

    // lob
    private static final String INFORMIX_CLOB = "CLOB";

    @Override
    public SeaTunnelDataType<?> mapping(ResultSetMetaData metadata, int colIndex)
            throws SQLException {
        String informixType = metadata.getColumnTypeName(colIndex).toUpperCase();
        int precision = metadata.getPrecision(colIndex);
        switch (informixType) {
            case INFORMIX_BOOLEAN:
                return BasicType.BOOLEAN_TYPE;
            case INFORMIX_SMALLINT:
                return BasicType.SHORT_TYPE;

            case INFORMIX_INT:
            case INFORMIX_INTEGER:
            case INFORMIX_SERIAL:
                return BasicType.INT_TYPE;
            case INFORMIX_BIGINT:
                return BasicType.LONG_TYPE;
            case INFORMIX_REAL:
                return BasicType.FLOAT_TYPE;
            case INFORMIX_FLOAT:
            case INFORMIX_SMALLFLOAT:
            case INFORMIX_DOUBLE_PRECISION:
                return BasicType.DOUBLE_TYPE;
            case INFORMIX_DECIMAL:
            case INFORMIX_NUMERIC:
            case INFORMIX_MONEY:
                if (precision > 0) {
                    return new DecimalType(precision, metadata.getScale(colIndex));
                }
                return new DecimalType(38, 18);
            case INFORMIX_CHAR:
            case INFORMIX_NCHAR:
            case INFORMIX_NVARCHAR:
            case INFORMIX_CHARACTER:
            case INFORMIX_VARCHAR:
            case INFORMIX_LVARCHAR:
            case INFORMIX_TEXT:
            case INFORMIX_CLOB:
            case INFORMIX_INTERVAL_DAY_TO_SECOND:
            case INFORMIX_INTERVAL_YEAR_TO_MONTH:
            case INFORMIX_JSON:
                return BasicType.STRING_TYPE;
            case INFORMIX_BYTE:
                return PrimitiveByteArrayType.INSTANCE;
            case INFORMIX_DATE:
                return LocalTimeType.LOCAL_DATE_TYPE;
            case INFORMIX_DATETIME:
            case INFORMIX_DATETIME_YEAR_TO_SECOND:
            case INFORMIX_DATETIME_YEAR_TO_FRACTION:
            case INFORMIX_DATETIME_YEAR_TO_FRACTION5:
            case INFORMIX_INTERVAL:
                return LocalTimeType.LOCAL_DATE_TIME_TYPE;
            default:
                final String jdbcColumnName = metadata.getColumnName(colIndex);
                throw CommonError.convertToSeaTunnelTypeError(
                        DatabaseIdentifier.DAMENG, informixType, jdbcColumnName);
        }
    }
}
