package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.impala;

import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialect;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialectFactory;

import com.google.auto.service.AutoService;

/**
 * <AUTHOR>
 * @date 2024/6/15
 */
@AutoService(JdbcDialectFactory.class)
public class ImpalaDialectFactory implements JdbcDialectFactory {
    @Override
    public boolean acceptsURL(String url) {
        return url.startsWith("jdbc:impala:");
    }

    @Override
    public JdbcDialect create() {
        return new ImpalaDialect();
    }
}
