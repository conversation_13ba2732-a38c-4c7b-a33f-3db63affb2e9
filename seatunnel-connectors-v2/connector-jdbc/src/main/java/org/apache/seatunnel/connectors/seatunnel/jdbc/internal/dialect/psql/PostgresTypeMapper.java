/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.psql;

import org.apache.seatunnel.api.table.type.ArrayType;
import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.DecimalType;
import org.apache.seatunnel.api.table.type.LocalTimeType;
import org.apache.seatunnel.api.table.type.PrimitiveByteArrayType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialectTypeMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;

public class PostgresTypeMapper implements JdbcDialectTypeMapper {

    private static final Logger LOG = LoggerFactory.getLogger(JdbcDialectTypeMapper.class);

    private static final String PG_SMALLSERIAL = "smallserial";
    private static final String PG_SERIAL = "serial";
    private static final String PG_BIGSERIAL = "bigserial";
    private static final String PG_BYTEA = "bytea";
    private static final String PG_BYTEA_ARRAY = "_bytea";
    private static final String PG_SMALLINT1 = "int1";
    private static final String PG_SMALLINT = "int2";
    private static final String PG_SMALLINT_ARRAY = "_int2";
    private static final String PG_INTEGER = "int4";
    private static final String PG_INTEGER_ARRAY = "_int4";
    private static final String PG_BIGINT = "int8";
    private static final String PG_BIGINT_ARRAY = "_int8";
    private static final String PG_REAL = "float4";
    private static final String PG_REAL_ARRAY = "_float4";
    private static final String PG_DOUBLE_PRECISION = "float8";
    private static final String PG_DOUBLE_PRECISION_ARRAY = "_float8";
    private static final String PG_NUMERIC = "numeric";
    private static final String PG_NUMERIC_ARRAY = "_numeric";
    private static final String PG_BOOLEAN = "bool";
    private static final String PG_BOOLEAN_ARRAY = "_bool";
    private static final String PG_TIMESTAMP = "timestamp";
    private static final String PG_TIMESTAMP_ARRAY = "_timestamp";
    private static final String PG_TIMESTAMPTZ = "timestamptz";
    private static final String PG_TIMESTAMPTZ_ARRAY = "_timestamptz";
    private static final String PG_DATE = "date";
    private static final String PG_DATE_ARRAY = "_date";
    private static final String PG_TIME = "time";
    private static final String PG_TIME_ARRAY = "_time";
    private static final String PG_TEXT = "text";
    private static final String PG_TEXT_ARRAY = "_text";
    private static final String PG_CHAR = "bpchar";
    private static final String PG_CHAR_ARRAY = "_bpchar";
    private static final String PG_CHARACTER = "character";
    private static final String PG_CHARACTER_ARRAY = "_character";
    private static final String PG_CHARACTER_VARYING = "varchar";
    private static final String PG_CHARACTER_VARYING2 = "varchar2";
    private static final String PG_CHARACTER_NVARYING2 = "nvarchar2";
    private static final String PG_CHARACTER_VARYING_ARRAY = "_varchar";
    private static final String PG_GEOMETRY = "geometry";
    private static final String PG_GEOGRAPHY = "geography";
    private static final String PG_JSON = "json";
    private static final String PG_JSONB = "jsonb";
    private static final String PG_XML = "xml";
    private static final String PG_UUID = "uuid";
    private static final String PG_UNKNOWN = "unknown";

    // TDsql type
    private static final String TDSQL_TIMESTAMP = "TIMESTAMP";
    private static final String TDSQL_CHARACTER_NVARYING2 = "NVARCHAR2";
    private static final String TDSQL_CHARACTER_VARYING2 = "VARCHAR2";
    private static final String TDSQL_DATE = "DATE";
    private static final String TDSQL_BLOB = "BLOB";
    private static final String TDSQL_CLOB = "CLOB";
    private static final String TDSQL_NCLOB = "NCLOB";
    private static final String TDSQL_NBLOB = "NBLOB";

    // GaussDB type
    // see：https://support.huaweicloud.com/distributed-devg-v2-gaussdb/gaussdb-12-0091.html
    private static final String GAUSSDB_OID = "oid";
    private static final String GAUSSDB_TINYINT = "tinyint";
    private static final String GAUSSDB_SMALLINT = "smallint";
    private static final String GAUSSDB_BIGINT = "bigint";
    private static final String GAUSSDB_CHAR = "char";
    private static final String GAUSSDB_CHARACTER_VARYING = "character varying";
    private static final String GAUSSDB_NAME = "name";
    private static final String GAUSSDB_BLOB = "blob";
    private static final String GAUSSDB_CLOB = "clob";
    private static final String GAUSSDB_TIMEZ = "timetz";
    private static final String GAUSSDB_SMALLDATETIME = "smalldatetime";
    private static final String GAUSSDB_REFCURSOR = "refcursor";

    // greenplum type see: https://docs-cn.greenplum.org/v6/ref_guide/data_types.html
    private static final String GREENPLUM_BIT = "bit";
    private static final String GREENPLUM_BIT_VARYING = "bit varying";
    private static final String GREENPLUM_BOOLEAN = "boolean";
    private static final String GREENPLUM_BOX = "box";
    private static final String GREENPLUM_CIDR = "cidr";
    private static final String GREENPLUM_CIRCLE = "circle";
    private static final String GREENPLUM_DECIMAL = "decimal";
    private static final String GREENPLUM_DOUBLE_PRECISION = "double precision";
    private static final String GREENPLUM_INET = "inet";
    private static final String GREENPLUM_INTERVAL = "interval";
    private static final String GREENPLUM_LSEG = "lseg";
    private static final String GREENPLUM_MACADDR = "macaddr";
    private static final String GREENPLUM_MONEY = "money";
    private static final String GREENPLUM_PATH = "path";
    private static final String GREENPLUM_POINT = "point";
    private static final String GREENPLUM_POLYGON = "polygon";
    private static final String GREENPLUM_REAL = "real";
    private static final String GREENPLUM_TIMESTAMP_WITH_TIME_ZONE = "timestamp with time zone";
    private static final String GREENPLUM_TIMESTAMP_WITHOUT_TIME_ZONE =
            "timestamp without time zone";
    private static final String GREENPLUM_TIME_WITH_TIME_ZONE = "time with time zone";

    @Override
    public SeaTunnelDataType<?> mapping(ResultSetMetaData metadata, int colIndex)
            throws SQLException {

        String pgType = metadata.getColumnTypeName(colIndex);

        int precision = metadata.getPrecision(colIndex);
        int scale = metadata.getScale(colIndex);

        switch (pgType) {
            case PG_BOOLEAN:
            case GREENPLUM_BOOLEAN:
            case GREENPLUM_BIT:
            case GREENPLUM_BIT_VARYING:
                return BasicType.BOOLEAN_TYPE;
            case PG_BOOLEAN_ARRAY:
                return ArrayType.BOOLEAN_ARRAY_TYPE;
            case PG_BYTEA:
                return PrimitiveByteArrayType.INSTANCE;
            case PG_BYTEA_ARRAY:
                return ArrayType.BYTE_ARRAY_TYPE;
            case PG_SMALLINT:
            case PG_SMALLINT1:
            case PG_SMALLSERIAL:
            case PG_INTEGER:
            case PG_SERIAL:
            case GAUSSDB_TINYINT:
            case GAUSSDB_SMALLINT:
                return BasicType.INT_TYPE;
            case PG_SMALLINT_ARRAY:
            case PG_INTEGER_ARRAY:
                return ArrayType.INT_ARRAY_TYPE;
            case PG_BIGINT:
            case PG_BIGSERIAL:
            case GAUSSDB_OID:
            case GAUSSDB_BIGINT:
                return BasicType.LONG_TYPE;
            case PG_BIGINT_ARRAY:
                return ArrayType.LONG_ARRAY_TYPE;
            case PG_REAL:
            case GREENPLUM_REAL:
                return BasicType.FLOAT_TYPE;
            case PG_REAL_ARRAY:
                return ArrayType.FLOAT_ARRAY_TYPE;
            case PG_DOUBLE_PRECISION:
            case GREENPLUM_DOUBLE_PRECISION:
            case GREENPLUM_MONEY:
                return BasicType.DOUBLE_TYPE;
            case PG_DOUBLE_PRECISION_ARRAY:
                return ArrayType.DOUBLE_ARRAY_TYPE;
            case PG_NUMERIC:
            case GREENPLUM_DECIMAL:
                // see SPARK-26538: handle numeric without explicit precision and scale.
                if (precision > 0) {
                    return new DecimalType(precision, metadata.getScale(colIndex));
                }
                return new DecimalType(38, 18);
            case PG_CHAR:
            case PG_CHARACTER:
            case PG_CHARACTER_VARYING:
            case PG_CHARACTER_VARYING2:
            case TDSQL_CHARACTER_VARYING2:
            case PG_CHARACTER_NVARYING2:
            case TDSQL_CHARACTER_NVARYING2:
            case PG_TEXT:
            case PG_UNKNOWN:
            case PG_GEOMETRY:
            case PG_GEOGRAPHY:
            case PG_JSON:
            case PG_JSONB:
            case PG_XML:
            case PG_UUID:
            case GAUSSDB_CLOB:
            case GAUSSDB_CHAR:
            case GAUSSDB_CHARACTER_VARYING:
            case GAUSSDB_NAME:
            case GAUSSDB_BLOB:
            case TDSQL_BLOB:
            case TDSQL_CLOB:
            case TDSQL_NCLOB:
            case TDSQL_NBLOB:
                return BasicType.STRING_TYPE;
            case PG_CHAR_ARRAY:
            case PG_CHARACTER_ARRAY:
            case PG_CHARACTER_VARYING_ARRAY:
            case PG_TEXT_ARRAY:
                return ArrayType.STRING_ARRAY_TYPE;
            case PG_TIMESTAMP:
            case PG_TIMESTAMPTZ:
            case TDSQL_DATE:
            case TDSQL_TIMESTAMP:
            case GREENPLUM_TIMESTAMP_WITH_TIME_ZONE:
            case GREENPLUM_TIMESTAMP_WITHOUT_TIME_ZONE:
            case GAUSSDB_SMALLDATETIME:
                return LocalTimeType.LOCAL_DATE_TIME_TYPE;
            case PG_TIME:
            case GREENPLUM_TIME_WITH_TIME_ZONE:
            case GAUSSDB_TIMEZ:
                return LocalTimeType.LOCAL_TIME_TYPE;
            case PG_DATE:
                return LocalTimeType.LOCAL_DATE_TYPE;
            case PG_TIMESTAMP_ARRAY:
            case PG_NUMERIC_ARRAY:
            case PG_TIMESTAMPTZ_ARRAY:
            case PG_TIME_ARRAY:
            case PG_DATE_ARRAY:
            default:
                final String jdbcColumnName = metadata.getColumnName(colIndex);
                throw CommonError.convertToSeaTunnelTypeError(
                        DatabaseIdentifier.POSTGRESQL, pgType, jdbcColumnName);
        }
    }
}
