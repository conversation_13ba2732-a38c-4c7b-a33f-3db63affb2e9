package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/8/9
 */
@Slf4j
@RequiredArgsConstructor
public class DeleteAndInsertBufferedExecutor implements JdbcBatchStatementExecutor<SeaTunnelRow> {
    @NonNull private final StatementFactory existStatementFactory;
    @NonNull private final StatementFactory statementFactory;
    @NonNull private final StatementFactory deleteStatementFactory;
    @NonNull private final TableSchema valueTableSchema;
    @NonNull private final TableSchema keyTableSchema;

    private final TableSchema databaseTableSchema;
    @NonNull private final Function<SeaTunnelRow, SeaTunnelRow> keyExtractor;
    @NonNull private final JdbcRowConverter rowConverter;

    // it is insert statement
    private transient PreparedStatement insertStatement;
    private transient PreparedStatement deleteStatement;
    private transient PreparedStatement existStatement;
    @NonNull private final Function<SeaTunnelRow, SeaTunnelRow> valueTransform;
    @NonNull private final List<SeaTunnelRow> buffer = new ArrayList<>();

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        insertStatement = statementFactory.createStatement(connection);
        deleteStatement = deleteStatementFactory.createStatement(connection);
        existStatement = existStatementFactory.createStatement(connection);
    }

    @Override
    public void addToBatch(SeaTunnelRow record) throws SQLException {
        buffer.add(valueTransform.apply(record));
        rowConverter.toExternal(keyTableSchema, keyExtractor.apply(record), deleteStatement);
        deleteStatement.addBatch();
        rowConverter.toExternal(valueTableSchema, record, insertStatement);
        insertStatement.addBatch();
    }

    @Override
    public void executeBatch() throws SQLException {
        if (!buffer.isEmpty()) {
            long startDeleteTime = System.currentTimeMillis();
            deleteStatement.executeBatch();
            deleteStatement.clearBatch();
            // cost
            log.info(
                    "DeleteAndInsertBufferedExecutor delete {} data cost :{}",
                    buffer.size(),
                    System.currentTimeMillis() - startDeleteTime);
            insertStatement.executeBatch();
            insertStatement.clearBatch();
            buffer.clear();
        }
    }

    @Override
    public void closeStatements() throws SQLException {
        if (!buffer.isEmpty()) {
            executeBatch();
        }
        insertStatement.close();
        deleteStatement.close();
    }
}
