package org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.sybase;

import org.apache.seatunnel.common.utils.JdbcUrlUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class SybaseURLParser {
    private static final int DEFAULT_PORT = 5000;

    public static JdbcUrlUtil.UrlInfo parse(String url) {
        if (url == null || !url.startsWith("jdbc:sybase:Tds:")) {
            return null;
        }
        String coreUrl = url.substring("jdbc:sybase:Tds:".length());
        String[] parts = coreUrl.split("\\?", 2);
        String hostPart = parts[0];
        Map<String, String> props = Collections.emptyMap();

        if (parts.length > 1) {
            props = parseQueryParams(parts[1], ";");
        }
        String host = "";
        int port = DEFAULT_PORT;
        String dbName = null;

        int slashIndex = hostPart.indexOf('/');
        if (slashIndex >= 0) {
            dbName = hostPart.substring(slashIndex + 1);
            hostPart = hostPart.substring(0, slashIndex);
        }

        int colonIndex = hostPart.indexOf(':');
        if (colonIndex > 0) {
            host = hostPart.substring(0, colonIndex);
            try {
                port = Integer.parseInt(hostPart.substring(colonIndex + 1));
            } catch (NumberFormatException e) {
                port = DEFAULT_PORT;
            }
        } else {
            host = hostPart;
        }

        if (props.containsKey("databaseName")) {
            dbName = props.get("databaseName");
        }

        String suffix =
                props.entrySet().stream()
                        .map(e -> e.getKey() + "=" + e.getValue())
                        .collect(Collectors.joining(";", "", ""));

        return new JdbcUrlUtil.UrlInfo(
                url,
                String.format("************************", host, port, dbName != null ? dbName : ""),
                host,
                port,
                dbName,
                suffix);
    }

    private static Map<String, String> parseQueryParams(String query, String separator) {
        if (query == null || query.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, String> queryParams = new LinkedHashMap<>();
        String[] pairs = query.split(separator);
        for (String pair : pairs) {
            try {
                int idx = pair.indexOf('=');
                String key =
                        idx > 0
                                ? URLDecoder.decode(
                                        pair.substring(0, idx), StandardCharsets.UTF_8.name())
                                : pair;
                if (!queryParams.containsKey(key)) {
                    String value =
                            (idx > 0 && pair.length() > idx + 1)
                                    ? URLDecoder.decode(
                                            pair.substring(idx + 1), StandardCharsets.UTF_8.name())
                                    : null;
                    queryParams.put(key, value);
                }
            } catch (UnsupportedEncodingException e) {
                // Ignore.
            }
        }
        return queryParams;
    }
}
