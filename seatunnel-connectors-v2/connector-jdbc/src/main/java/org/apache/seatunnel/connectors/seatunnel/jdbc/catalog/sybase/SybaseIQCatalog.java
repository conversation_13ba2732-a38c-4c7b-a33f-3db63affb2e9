package org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.sybase;

import org.apache.seatunnel.api.table.catalog.TablePath;
import org.apache.seatunnel.common.utils.JdbcUrlUtil;

import org.apache.commons.lang3.StringUtils;

import java.sql.ResultSet;
import java.sql.SQLException;

public class SybaseIQ<PERSON>atalog extends SybaseCatalog {

    private static final String SELECT_COLUMNS_SQL_TEMPLATE =
            "SELECT t.table_name AS table_name,\n"
                    + "       c.column_name AS column_name,\n"
                    + "       c.column_id AS column_id,\n"
                    + "       d.domain_name AS type,\n"
                    + "       c.width AS max_length,\n"
                    + "       d.[precision] AS precision_v,\n"
                    + "       c.scale AS scale,\n"
                    + "       CASE WHEN c.nulls = 'Y' THEN 1 ELSE 0 END AS is_nullable,\n"
                    + "       c.\"default\" AS default_value\n"
                    + "FROM SYS.SYSTABLE t\n"
                    + "JOIN SYS.SYSCOLUMN c ON t.table_id = c.table_id\n"
                    + "JOIN SYS.SYSDOMAIN d ON c.domain_id = d.domain_id\n"
                    + "WHERE t.creator = USER_ID('%s')\n"
                    + "  AND t.table_type = 'BASE' and t.table_name = '%s'\n"
                    + "ORDER BY t.table_name, c.column_id";

    public SybaseIQCatalog(
            String catalogName,
            String username,
            String pwd,
            JdbcUrlUtil.UrlInfo urlInfo,
            String defaultSchema,
            String driverClass) {
        super(catalogName, username, pwd, urlInfo, defaultSchema, driverClass);
    }

    protected String getTableName(ResultSet rs) throws SQLException {
        String schemaName = rs.getString(1);
        String tableName = rs.getString(2).trim();
        if (StringUtils.isNotBlank(schemaName) && !SYS_DATABASES.contains(schemaName)) {
            return schemaName + "." + tableName;
        }
        return null;
    }

    @Override
    protected String getListDatabaseSql() {
        return "SELECT DB_NAME() AS database_name";
    }

    @Override
    protected String getListTableSql(String databaseName) {
        return "SELECT \n"
                + "    user_name(creator) AS TABLE_SCHEMA,\n"
                + "    table_name AS TABLE_NAME\n"
                + "FROM \n"
                + "    sys.systable\n"
                + "WHERE \n"
                + "    table_type = 'BASE'";
    }

    @Override
    protected String getSelectColumnsSql(TablePath tablePath) {
        return String.format(
                SELECT_COLUMNS_SQL_TEMPLATE, tablePath.getSchemaName(), tablePath.getTableName());
    }
}
