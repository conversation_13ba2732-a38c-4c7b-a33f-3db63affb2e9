/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect;

public class DatabaseIdentifier {
    public static final String DB_2 = "DB2";
    public static final String DB_2_DRIVER = "com.ibm.db2.jcc.DB2Driver";
    public static final String DAMENG = "Dameng";
    public static final String DAMENG_DRIVER = "dm.jdbc.driver.DmDriver";
    public static final String GBASE_8A = "Gbase8a";
    public static final String GBASE_8A_DRIVER = "com.gbase.jdbc.Driver";
    public static final String HIVE = "HIVE";
    public static final String HIVE_DRIVER = "org.apache.hive.jdbc.HiveDriver";
    public static final String INFORMIX = "Informix";
    public static final String INFORMIX_DRIVER = "com.informix.jdbc.IfxDriver";
    public static final String KINGBASE = "KingBase";
    public static final String KINGBASE_DRIVER = "com.kingbase8.Driver";
    public static final String MYSQL = "MySQL";
    public static final String MYSQL_DRIVER = "com.mysql.cj.jdbc.Driver";
    public static final String ORACLE = "Oracle";
    public static final String ORACLE_DRIVER = "oracle.jdbc.OracleDriver";
    public static final String PHOENIX = "Phoenix";
    public static final String PHOENIX_DRIVER = "org.apache.phoenix.queryserver.client.Driver";
    public static final String POSTGRESQL = "Postgres";
    public static final String POSTGRESQL_DRIVER = "org.postgresql.Driver";
    public static final String REDSHIFT = "Redshift";
    public static final String REDSHIFT_DRIVER = "com.amazon.redshift.jdbc42.Driver";
    public static final String SAP_HANA = "SapHana";
    public static final String SAP_HANA_DRIVER = "com.sap.db.jdbc.Driver";
    public static final String SNOWFLAKE = "Snowflake";
    public static final String SNOWFLAKE_DRIVER = "net.snowflake.client.jdbc.SnowflakeDriver";
    public static final String SQLITE = "Sqlite";
    public static final String SQLITE_DRIVER = "org.sqlite.JDBC";
    public static final String SQLSERVER = "SqlServer";
    public static final String SQLSERVER_DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
    public static final String SYBASE = "Sybase";
    public static final String SYBASE_DRIVER = "com.sybase.jdbc4.jdbc.SybDriver";
    public static final String TABLE_STORE = "Tablestore";
    public static final String TABLE_STORE_DRIVER = "Tablestore";
    public static final String TERADATA = "Teradata";
    public static final String TERADATA_DRIVER = "com.teradata.jdbc.TeraDriver";
    public static final String VERTICA = "Vertica";
    public static final String VERTICA_DRIVER = "com.vertica.jdbc.Driver";
    public static final String OCENABASE = "OceanBase";
    public static final String OCENABASE_DRIVER = "com.oceanbase.jdbc.Driver";
    public static final String TIDB = "TiDB";
    public static final String TIDB_DRIVER = "com.mysql.cj.jdbc.Driver";
    public static final String GREENPLUM = "Greenplum";
    public static final String GREENPLUM_DRIVER = "com.pivotal.jdbc.GreenplumDriver";
    // 切换 schema 命令
    public static final String SWITCH_SCHEMA = "set search_path to %s";
    public static final String IMPALA = "Impala";
    public static final String IMPALA_DRIVER = "com.cloudera.impala.jdbc.Driver";
    public static final String AS400 = "as400";
    public static final String AS400_DRIVER = "com.ibm.as400.access.AS400JDBCDriver";

    public static final String GAUSSDB200 = "GaussDB200";
    public static final String GAUSSDB200_DRIVER = "com.huawei.gauss200.jdbc.Driver";
}
