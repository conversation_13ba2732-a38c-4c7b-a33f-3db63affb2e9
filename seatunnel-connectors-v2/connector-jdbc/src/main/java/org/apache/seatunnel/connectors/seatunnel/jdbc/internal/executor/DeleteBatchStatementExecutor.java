package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Slf4j
@RequiredArgsConstructor
public class DeleteBatchStatementExecutor implements JdbcBatchStatementExecutor<SeaTunnelRow> {
    @NonNull private final StatementFactory deleteStmtFactory;
    private final TableSchema keyTableSchema;
    private final Function<SeaTunnelRow, SeaTunnelRow> keyExtractor;
    private final TableSchema databaseTableSchema;
    @NonNull private final JdbcRowConverter rowConverter;

    private transient PreparedStatement deleteStatement;

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        deleteStatement = deleteStmtFactory.createStatement(connection);
    }

    @Override
    public void addToBatch(SeaTunnelRow record) throws SQLException {
        rowConverter.toExternal(keyTableSchema, keyExtractor.apply(record), deleteStatement);
        deleteStatement.addBatch();
    }

    @Override
    public void executeBatch() throws SQLException {
        deleteStatement.executeBatch();
        deleteStatement.clearBatch();
    }

    @Override
    public void closeStatements() throws SQLException {
        executeBatch();
        deleteStatement.close();
    }
}
