package org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.sybase;

import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.catalog.Catalog;
import org.apache.seatunnel.api.table.factory.CatalogFactory;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.common.utils.JdbcUrlUtil;
import org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.JdbcCatalogOptions;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;

import org.apache.commons.lang3.StringUtils;

import com.google.auto.service.AutoService;

@AutoService(Factory.class)
public class SybaseCatalogFactory implements CatalogFactory {

    @Override
    public String factoryIdentifier() {
        return DatabaseIdentifier.SYBASE;
    }

    @Override
    public Catalog createCatalog(String catalogName, ReadonlyConfig options) {
        String url = options.get(JdbcCatalogOptions.BASE_URL);
        JdbcUrlUtil.UrlInfo urlInfo = SybaseURLParser.parse(url);
        String compatibleMode = options.get(JdbcCatalogOptions.COMPATIBLE_MODE);
        if (StringUtils.equals(compatibleMode, "sybase_iq")) {
            return new SybaseIQCatalog(
                    catalogName,
                    options.get(JdbcCatalogOptions.USERNAME),
                    options.get(JdbcCatalogOptions.PASSWORD),
                    urlInfo,
                    options.get(JdbcCatalogOptions.SCHEMA),
                    options.get(
                            Options.key("driverName")
                                    .stringType()
                                    .defaultValue(DatabaseIdentifier.SYBASE)));
        }
        return new SybaseCatalog(
                catalogName,
                options.get(JdbcCatalogOptions.USERNAME),
                options.get(JdbcCatalogOptions.PASSWORD),
                urlInfo,
                options.get(JdbcCatalogOptions.SCHEMA),
                options.get(
                        Options.key("driverName")
                                .stringType()
                                .defaultValue(DatabaseIdentifier.SYBASE)));
    }

    @Override
    public OptionRule optionRule() {
        return JdbcCatalogOptions.BASE_RULE.build();
    }
}
