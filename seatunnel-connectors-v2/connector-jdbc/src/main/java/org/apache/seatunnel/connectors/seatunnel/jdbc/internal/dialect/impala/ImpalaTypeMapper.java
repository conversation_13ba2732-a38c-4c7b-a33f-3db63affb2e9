package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.impala;

import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.DecimalType;
import org.apache.seatunnel.api.table.type.LocalTimeType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialectTypeMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/6/15
 */
public class ImpalaTypeMapper implements JdbcDialectTypeMapper {
    private static final Logger LOG = LoggerFactory.getLogger(ImpalaTypeMapper.class);
    // Numeric Types
    private static final String IMPALA_TINYINT = "TINYINT";
    private static final String IMPALA_SMALLINT = "SMALLINT";
    private static final String IMPALA_INT = "INT";
    private static final String IMPALA_INTEGER = "INTEGER";
    private static final String IMPALA_BIGINT = "BIGINT";
    private static final String IMPALA_FLOAT = "FLOAT";
    private static final String IMPALA_DOUBLE = "DOUBLE";
    private static final String IMPALA_DOUBLE_PRECISION = "DOUBLE PRECISION";
    private static final String IMPALA_DECIMAL = "DECIMAL";
    private static final String IMPALA_NUMERIC = "NUMERIC";
    // Date/Time Types
    private static final String IMPALA_TIMESTAMP = "TIMESTAMP";
    private static final String IMPALA_DATE = "DATE";
    private static final String IMPALA_INTERVAL = "INTERVAL";
    // String Types
    private static final String IMPALA_STRING = "STRING";
    private static final String IMPALA_VARCHAR = "VARCHAR";
    private static final String IMPALA_CHAR = "CHAR";
    // Misc Types
    private static final String IMPALA_BOOLEAN = "BOOLEAN";
    private static final String IMPALA_BINARY = "BINARY";
    // Complex Types
    private static final String IMPALA_ARRAY = "ARRAY";
    private static final String IMPALA_MAP = "MAP";
    private static final String IMPALA_STRUCT = "STRUCT";
    private static final String IMPALA_UNIONTYPE = "UNIONTYPE";

    @Override
    public SeaTunnelDataType<?> mapping(ResultSetMetaData metadata, int colIndex)
            throws SQLException {
        String columnType = metadata.getColumnTypeName(colIndex).toUpperCase();
        int precision = metadata.getPrecision(colIndex);
        switch (columnType) {
            case IMPALA_TINYINT:
                return BasicType.BYTE_TYPE;
            case IMPALA_SMALLINT:
                return BasicType.SHORT_TYPE;
            case IMPALA_INT:
            case IMPALA_INTEGER:
                return BasicType.INT_TYPE;
            case IMPALA_BIGINT:
                return BasicType.LONG_TYPE;
            case IMPALA_FLOAT:
                return BasicType.FLOAT_TYPE;
            case IMPALA_DOUBLE:
            case IMPALA_DOUBLE_PRECISION:
                return BasicType.DOUBLE_TYPE;
            case IMPALA_DECIMAL:
            case IMPALA_NUMERIC:
                if (precision > 0) {
                    return new DecimalType(precision, metadata.getScale(colIndex));
                }
                LOG.warn("decimal did define precision,scale, will be Decimal(38,18)");
                return new DecimalType(38, 18);
            case IMPALA_TIMESTAMP:
                return LocalTimeType.LOCAL_DATE_TIME_TYPE;
            case IMPALA_DATE:
                return LocalTimeType.LOCAL_DATE_TYPE;
            case IMPALA_STRING:
            case IMPALA_VARCHAR:
            case IMPALA_CHAR:
                return BasicType.STRING_TYPE;
            case IMPALA_BOOLEAN:
                return BasicType.BOOLEAN_TYPE;
            case IMPALA_BINARY:
            case IMPALA_ARRAY:
            case IMPALA_INTERVAL:
            case IMPALA_MAP:
            case IMPALA_STRUCT:
            case IMPALA_UNIONTYPE:
            default:
                final String jdbcColumnName = metadata.getColumnName(colIndex);
                throw CommonError.convertToSeaTunnelTypeError(
                        DatabaseIdentifier.IMPALA, columnType, jdbcColumnName);
        }
    }
}
