/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection;

import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcConnectionConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;

import org.apache.commons.lang3.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import lombok.NonNull;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.Properties;

import static com.google.common.base.Preconditions.checkNotNull;

/** Simple JDBC connection provider. */
public class SimpleJdbcConnectionProvider implements JdbcConnectionProvider, Serializable {

    private static final Logger LOG = LoggerFactory.getLogger(SimpleJdbcConnectionProvider.class);

    private static final long serialVersionUID = 1L;

    protected final JdbcConnectionConfig jdbcConfig;

    private transient Driver loadedDriver;
    protected transient Connection connection;

    public SimpleJdbcConnectionProvider(@NonNull JdbcConnectionConfig jdbcConfig) {
        this.jdbcConfig = jdbcConfig;
    }

    @Override
    public Connection getConnection() {
        return connection;
    }

    @Override
    public boolean isConnectionValid() throws SQLException {
        return connection != null
                && (!jdbcConfig.getUrl().startsWith("jdbc:as400:")
                        && connection.isValid(jdbcConfig.getConnectionCheckTimeoutSeconds()));
    }

    private static Driver loadDriver(String driverName) throws ClassNotFoundException {
        checkNotNull(driverName);
        Enumeration<Driver> drivers = DriverManager.getDrivers();
        while (drivers.hasMoreElements()) {
            Driver driver = drivers.nextElement();
            if (driver.getClass().getName().equals(driverName)) {
                return driver;
            }
        }

        // We could reach here for reasons:
        // * Class loader hell of DriverManager(see JDK-8146872).
        // * driver is not installed as a service provider.
        Class<?> clazz =
                Class.forName(driverName, true, Thread.currentThread().getContextClassLoader());
        try {
            return (Driver) clazz.getDeclaredConstructor().newInstance();
        } catch (Exception ex) {
            throw new JdbcConnectorException(
                    JdbcConnectorErrorCode.CREATE_DRIVER_FAILED,
                    "Fail to create driver of class " + driverName,
                    ex);
        }
    }

    protected Driver getLoadedDriver() throws SQLException, ClassNotFoundException {
        if (loadedDriver == null) {
            loadedDriver = loadDriver(jdbcConfig.getDriverName());
        }
        return loadedDriver;
    }

    @Override
    public Connection getOrEstablishConnection() throws SQLException, ClassNotFoundException {
        if (isConnectionValid()) {
            return connection;
        }
        Driver driver = getLoadedDriver();
        Properties info = new Properties();
        /*info.put("db2.jcc.charsetDecoderEncoder", "3");  // 修复字符转换错误
        info.put("charsetEncoding", "UTF-8");           // 强制使用 UTF-8 编码
        info.put("db2.jcc.clobCharacterSet", "UTF-8");  // 确保 CLOB 使用 UTF-8*/
        if (jdbcConfig.getUrl().startsWith("jdbc:db2")) {
            System.setProperty("db2.jcc.charsetDecoderEncoder", "3");
        }
        if (jdbcConfig.getUsername().isPresent()) {
            info.setProperty("user", jdbcConfig.getUsername().get());
        }
        if (jdbcConfig.getPassword().isPresent()) {
            info.setProperty("password", jdbcConfig.getPassword().get());
        }
        info.putAll(jdbcConfig.getProperties());
        connection = driver.connect(jdbcConfig.getUrl(), info);

        if (connection == null) {
            // Throw same exception as DriverManager.getConnection when no driver found to match
            // caller expectation.
            throw new JdbcConnectorException(
                    JdbcConnectorErrorCode.NO_SUITABLE_DRIVER,
                    "No suitable driver found for " + jdbcConfig.getUrl());
        }
        if (StringUtils.isNotEmpty(jdbcConfig.getSchema())) {
            String driverName = jdbcConfig.getDriverName();
            String schema = jdbcConfig.getSchema();
            if (DatabaseIdentifier.GREENPLUM_DRIVER.equals(driverName)) {
                // 2025-04-01 GREENPLUM_DRIVER没有setSchema()，需要用sql语句切换
                connection
                        .createStatement()
                        .execute(String.format(DatabaseIdentifier.SWITCH_SCHEMA, schema));
            } else {
                connection.setSchema(schema);
            }
        }
        connection.setAutoCommit(jdbcConfig.isAutoCommit());

        return connection;
    }

    @Override
    public void closeConnection() {
        try {
            if (isConnectionValid()) {
                connection.close();
            }
        } catch (SQLException e) {
            LOG.warn("JDBC connection close failed.", e);
        } finally {
            connection = null;
        }
    }

    @Override
    public Connection reestablishConnection() throws SQLException, ClassNotFoundException {
        closeConnection();
        return getOrEstablishConnection();
    }

    public JdbcConnectionConfig getJdbcConfig() {
        return jdbcConfig;
    }

    public void setConnection(Connection connection) {
        this.connection = connection;
    }
}
