package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.impala;

import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcConnectionConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection.SimpleJdbcConnectionProvider;

import lombok.NonNull;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.SQLException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/6/15
 */
public class ImpalaJdbcConnectionProvider extends SimpleJdbcConnectionProvider {
    public ImpalaJdbcConnectionProvider(@NonNull JdbcConnectionConfig jdbcConfig) {
        super(jdbcConfig);
    }

    @Override
    public Connection getOrEstablishConnection() throws SQLException, ClassNotFoundException {
        if (isConnectionValid()) {
            return super.getConnection();
        }
        JdbcConnectionConfig jdbcConfig = super.getJdbcConfig();
        final Driver driver = getLoadedDriver();
        ImpalaConnectionProduceFunction impalaConnectionProduceFunction =
                new ImpalaConnectionProduceFunction(driver, jdbcConfig);
        if (jdbcConfig.useKerberos) {
            // super.setConnection(impalaConnectionProduceFunction.produce());
        } else {
            super.setConnection(impalaConnectionProduceFunction.produce());
        }
        if (null == super.getConnection()) {
            throw new JdbcConnectorException(
                    JdbcConnectorErrorCode.NO_SUITABLE_DRIVER,
                    "No suitable driver found for " + super.getJdbcConfig().getUrl());
        }
        return super.getConnection();
    }

    public static class ImpalaConnectionProduceFunction {
        private final Driver driver;
        private final JdbcConnectionConfig jdbcConnectionConfig;

        public ImpalaConnectionProduceFunction(
                Driver driver, JdbcConnectionConfig jdbcConnectionConfig) {
            this.driver = driver;
            this.jdbcConnectionConfig = jdbcConnectionConfig;
        }

        public Connection produce() throws SQLException {
            final Properties info = new Properties();
            jdbcConnectionConfig
                    .getUsername()
                    .ifPresent(username -> info.setProperty("user", username));
            jdbcConnectionConfig
                    .getPassword()
                    .ifPresent(password -> info.setProperty("password", password));
            return driver.connect(jdbcConnectionConfig.getUrl(), info);
        }
    }
}
