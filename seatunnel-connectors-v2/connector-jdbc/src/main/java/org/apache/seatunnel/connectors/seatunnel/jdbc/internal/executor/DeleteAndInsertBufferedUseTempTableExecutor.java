package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@Slf4j
@RequiredArgsConstructor
public class DeleteAndInsertBufferedUseTempTableExecutor
        implements JdbcBatchStatementExecutor<SeaTunnelRow> {
    @NonNull private final StatementFactory insertStatementFactory;
    @NonNull private final StatementFactory deleteStatementFactory;
    @NonNull private final StatementFactory insertTempStatementFactory;
    @NonNull private final StatementFactory deleteTempTableStatementFactory;
    @NonNull private final TableSchema valueTableSchema;
    @NonNull private final TableSchema keyTableSchema;
    @NonNull private final Function<SeaTunnelRow, SeaTunnelRow> keyExtractor;
    private final TableSchema databaseTableSchema;
    @NonNull private final JdbcRowConverter rowConverter;

    // it is insert statement
    private transient PreparedStatement insertStatement;
    private transient PreparedStatement insertTempStatement;
    private transient PreparedStatement deleteStatement;
    private transient PreparedStatement deleteTempTableStatement;
    @NonNull private final Function<SeaTunnelRow, SeaTunnelRow> valueTransform;
    @NonNull private final List<SeaTunnelRow> buffer = new ArrayList<>();

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        insertStatement = insertStatementFactory.createStatement(connection);
        insertTempStatement = insertTempStatementFactory.createStatement(connection);
        deleteStatement = deleteStatementFactory.createStatement(connection);
        deleteTempTableStatement = deleteTempTableStatementFactory.createStatement(connection);
    }

    @Override
    public void addToBatch(SeaTunnelRow record) throws SQLException {
        buffer.add(valueTransform.apply(record));
        // insert table data add batch
        rowConverter.toExternal(valueTableSchema, record, insertStatement);
        insertStatement.addBatch();
        // insert tmp table data add batch
        rowConverter.toExternal(keyTableSchema, keyExtractor.apply(record), insertTempStatement);
        insertTempStatement.addBatch();
        // delete tmp batch
        rowConverter.toExternal(
                keyTableSchema, keyExtractor.apply(record), deleteTempTableStatement);
        deleteTempTableStatement.addBatch();
    }

    @Override
    public void executeBatch() throws SQLException {
        if (!buffer.isEmpty()) {
            // insert tmp table data
            insertTempStatement.executeBatch();
            long startDeleteTime = System.currentTimeMillis();
            // delete sink table data
            deleteStatement.execute();
            log.info(
                    "DeleteAndInsertBufferedUseTempTableExecutor delete {} data cost :{}",
                    buffer.size(),
                    System.currentTimeMillis() - startDeleteTime);
            // insert sink table data
            insertStatement.executeBatch();
            insertStatement.clearBatch();
            // truncate temp table data
            deleteTempTableStatement.executeBatch();
            deleteTempTableStatement.clearBatch();
            buffer.clear();
        }
    }

    @Override
    public void closeStatements() throws SQLException {
        if (!buffer.isEmpty()) {
            executeBatch();
        }
        insertStatement.close();
        insertTempStatement.close();
        deleteStatement.close();
        deleteTempTableStatement.close();
    }
}
