package org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.sybase;

import org.apache.commons.lang3.tuple.Pair;

import com.google.common.collect.ImmutableMap;

import java.math.BigDecimal;
import java.sql.SQLType;
import java.sql.Types;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public enum SybaseType implements SQLType {
    // ============================unknown============================
    UNKNOWN("unknown", 999, Object.class),
    // int
    TINYINT("tinyint", java.sql.Types.TINYINT, Short.class),
    SMALLINT("smallint", java.sql.Types.SMALLINT, Short.class),
    INT("int", java.sql.Types.INTEGER, Integer.class),
    INTEGER("integer", java.sql.Types.INTEGER, Integer.class),
    BIGINT("bigint", java.sql.Types.BIGINT, Long.class),
    // ============================number============================
    FLOAT("float", java.sql.Types.DOUBLE, Double.class),
    DOUBLE_PRECISION("DOUBLE PRECISION", java.sql.Types.DOUBLE, Double.class),
    REAL("real", java.sql.Types.REAL, Float.class),
    SMALLMONEY("smallmoney", java.sql.Types.DECIMAL, BigDecimal.class),
    MONEY("money", java.sql.Types.DECIMAL, BigDecimal.class),
    DECIMAL("decimal", java.sql.Types.DECIMAL, BigDecimal.class, true, true),
    NUMERIC("numeric", java.sql.Types.NUMERIC, BigDecimal.class),
    // ============================string============================
    CHAR("char", java.sql.Types.CHAR, String.class),
    NCHAR("nchar", java.sql.Types.NCHAR, String.class),
    UNICHAR("unichar", java.sql.Types.CHAR, String.class),
    VARCHAR("varchar", java.sql.Types.VARCHAR, String.class),
    NVARCHAR("nvarchar", Types.NVARCHAR, String.class),
    UNIVARCHAR("univarchar", java.sql.Types.CHAR, String.class),
    TEXT("text", java.sql.Types.LONGVARCHAR, String.class),
    LONGVARCHAR("long varchar", java.sql.Types.LONGVARCHAR, String.class),
    SYSNAME("sysname", java.sql.Types.VARCHAR, String.class),
    LONGSYSNAME("longsysname", java.sql.Types.VARCHAR, String.class),
    // ============================datetime============================
    SMALLDATETIME("smalldatetime", java.sql.Types.TIMESTAMP, java.sql.Timestamp.class),
    DATETIME("datetime", java.sql.Types.TIMESTAMP, java.sql.Timestamp.class),
    BIGDATETIME("bigdatetime", java.sql.Types.TIMESTAMP, java.sql.Timestamp.class),
    DATE("date", java.sql.Types.DATE, java.sql.Date.class),
    TIME("time", java.sql.Types.TIME, java.sql.Time.class),
    BIGTIME("bigtime", java.sql.Types.TIME, java.sql.Time.class),
    TIMESTAMP("timestamp", java.sql.Types.TIMESTAMP, byte[].class),
    // ============================ blob ============================
    BINARY("binary", java.sql.Types.BINARY, byte[].class),
    VARBINARY("varbinary", java.sql.Types.VARBINARY, byte[].class),
    IMAGE("image", java.sql.Types.LONGVARBINARY, byte[].class),
    BIT("bit", java.sql.Types.BIT, Boolean.class),

    // sybase iq type
    IQ_DOUBLE("double", java.sql.Types.DOUBLE, Double.class),
    IQ_LONG_BINARY("long binary", java.sql.Types.LONGVARBINARY, byte[].class),
    IQ_UNIQUEIDENTIFIER("uniqueidentifier", java.sql.Types.VARCHAR, String.class);

    private static final String PRECISION = "precision";
    private static final String SCALE = "scale";
    private static final String LENGTH = "length";

    private final String name;
    private final int jdbcType;
    private final Class<?> javaClass;
    private final boolean isDecimal;
    private final boolean hasLength;

    SybaseType(String sybaseTypeName, int jdbcType, Class<?> javaClass) {
        this(sybaseTypeName, jdbcType, javaClass, false, false);
    }

    SybaseType(
            String sybaseTypeName,
            int jdbcType,
            Class<?> javaClass,
            boolean isDec,
            boolean hasLength) {
        this.name = sybaseTypeName;
        this.jdbcType = jdbcType;
        this.javaClass = javaClass;
        this.isDecimal = isDec;
        this.hasLength = hasLength;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getVendor() {
        return "com.sybase.jdbc4.jdbc.SybDriver";
    }

    @Override
    public Integer getVendorTypeNumber() {
        return jdbcType;
    }

    public String getSqlTypeName(Map<String, Object> params) {
        if (isDecimal) {
            Object precision = params.get(PRECISION);
            Object scale = params.get(SCALE);
            return String.format("%s(%s, %s)", getName(), precision, scale);
        }
        if (hasLength) {
            Object length = params.get(LENGTH);
            return String.format("%s(%s)", getName(), length);
        }
        return getName();
    }

    public String getSqlTypeName() {
        return getSqlTypeName(Collections.emptyMap());
    }

    public String getSqlTypeName(long length) {
        return getSqlTypeName(Collections.singletonMap(LENGTH, length));
    }

    public String getSqlTypeName(long precision, long scale) {
        return getSqlTypeName(ImmutableMap.of(PRECISION, precision, SCALE, scale));
    }

    public static Pair<SybaseType, Map<String, Object>> parse(String fullTypeName) {
        Map<String, Object> params = new HashMap<>();
        String typeName = fullTypeName;
        if (fullTypeName.indexOf("(") != -1) {
            typeName = fullTypeName.substring(0, fullTypeName.indexOf("(")).trim();
            String paramsStr =
                    fullTypeName.substring(
                            fullTypeName.indexOf("(") + 1, fullTypeName.indexOf(")"));
            if (DECIMAL.getName().equalsIgnoreCase(typeName)) {
                String[] precisionAndScale = paramsStr.split(",");
                params.put(PRECISION, precisionAndScale[0].trim());
                params.put(SCALE, precisionAndScale[1].trim());
            } else {
                params.put(LENGTH, paramsStr.trim());
            }
        }

        SybaseType sybaseType = null;
        for (SybaseType type : SybaseType.values()) {
            if (type.getName().equalsIgnoreCase(typeName)) {
                sybaseType = type;
                break;
            }
        }
        Objects.requireNonNull(sybaseType);
        return Pair.of(sybaseType, params);
    }
}
