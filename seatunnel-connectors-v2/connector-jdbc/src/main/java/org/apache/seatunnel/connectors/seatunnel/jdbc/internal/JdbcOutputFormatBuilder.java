/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal;

import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.TablePath;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcSinkConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialect;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.BufferReducedBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.BufferedBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.CopyManagerBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.DeleteAndInsertBufferedExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.DeleteAndInsertBufferedUseTempTableExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.DeleteBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.ErrorStrategyBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.FieldNamedPreparedStatement;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.HiveBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.InsertOrUpdateBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.JdbcBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.SimpleBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.utils.ConflictStrategyEnum;

import org.apache.commons.lang3.StringUtils;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.IntFunction;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkState;
import static org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.FieldNamedPreparedStatement.getStatementSql;

@Slf4j
@RequiredArgsConstructor
public class JdbcOutputFormatBuilder {
    @NonNull private final JdbcDialect dialect;
    @NonNull private final JdbcConnectionProvider connectionProvider;
    @NonNull private final JdbcSinkConfig jdbcSinkConfig;
    @NonNull private final TableSchema tableSchema;

    private final TableSchema databaseTableSchema;

    public JdbcOutputFormat build() {
        JdbcOutputFormat.StatementExecutorFactory statementExecutorFactory;

        final String database = jdbcSinkConfig.getDatabase();
        final String table =
                dialect.extractTableName(
                        TablePath.of(
                                jdbcSinkConfig.getDatabase() + "." + jdbcSinkConfig.getTable()));
        final List<String> primaryKeys = jdbcSinkConfig.getPrimaryKeys();
        if (jdbcSinkConfig.isUseCopyStatement()) {
            statementExecutorFactory =
                    () ->
                            createCopyInBufferStatementExecutor(
                                    createCopyInBatchStatementExecutor(
                                            dialect, table, tableSchema, databaseTableSchema));
        } else if (StringUtils.isNotBlank(jdbcSinkConfig.getSimpleSql())) {
            statementExecutorFactory =
                    () ->
                            createSimpleBufferedExecutor(
                                    jdbcSinkConfig.getSimpleSql(),
                                    tableSchema,
                                    databaseTableSchema,
                                    dialect.getRowConverter());
        } else if (jdbcSinkConfig
                .getJdbcConnectionConfig()
                .url
                .toLowerCase()
                .startsWith("jdbc:hive2")) {
            statementExecutorFactory =
                    () ->
                            createHiveBufferedExecutor(
                                    dialect, database, table, tableSchema, jdbcSinkConfig);
        } else if (primaryKeys == null || primaryKeys.isEmpty()) {
            statementExecutorFactory =
                    () ->
                            createSimpleBufferedExecutor(
                                    dialect, database, table, tableSchema, databaseTableSchema);
        } else if (ConflictStrategyEnum.ONLY_DELETE_CONFLICTING_ROWS
                .getValue()
                .equalsIgnoreCase(jdbcSinkConfig.getConflictStrategyRow())) {
            statementExecutorFactory =
                    () ->
                            createDeleteConflictExecutor(
                                    dialect,
                                    database,
                                    table,
                                    tableSchema,
                                    databaseTableSchema,
                                    primaryKeys.toArray(new String[0]),
                                    jdbcSinkConfig.isEnableUpsert(),
                                    jdbcSinkConfig.isPrimaryKeyUpdated(),
                                    jdbcSinkConfig.isSupportUpsertByInsertOnly());
        } else if (ConflictStrategyEnum.ONLY_UPDATE_CONFLICTING_ROWS
                .getValue()
                .equalsIgnoreCase(jdbcSinkConfig.getConflictStrategyRow())) {
            statementExecutorFactory =
                    () ->
                            createUpdateOnlyConflictExecutor(
                                    dialect,
                                    database,
                                    table,
                                    tableSchema,
                                    databaseTableSchema,
                                    primaryKeys.toArray(new String[0]),
                                    jdbcSinkConfig.isEnableUpsert(),
                                    jdbcSinkConfig.isPrimaryKeyUpdated(),
                                    jdbcSinkConfig.isSupportUpsertByInsertOnly());
        } else if (ConflictStrategyEnum.DELETE_CONFLICTING_BEFORE_INSERTING_ROWS
                .getValue()
                .equalsIgnoreCase(jdbcSinkConfig.getConflictStrategyRow())) {
            statementExecutorFactory =
                    () ->
                            createDeleteFirstInsertAfterExecutor(
                                    dialect,
                                    database,
                                    table,
                                    tableSchema,
                                    databaseTableSchema,
                                    primaryKeys.toArray(new String[0]),
                                    jdbcSinkConfig.isEnableUpsert(),
                                    jdbcSinkConfig.isPrimaryKeyUpdated(),
                                    jdbcSinkConfig.isSupportUpsertByInsertOnly());
        } else {
            statementExecutorFactory =
                    () ->
                            createUpsertBufferedExecutor(
                                    dialect,
                                    database,
                                    table,
                                    tableSchema,
                                    databaseTableSchema,
                                    primaryKeys.toArray(new String[0]),
                                    jdbcSinkConfig.isEnableUpsert(),
                                    jdbcSinkConfig.isPrimaryKeyUpdated(),
                                    jdbcSinkConfig.isSupportUpsertByInsertOnly());
        }
        return new JdbcOutputFormat(
                connectionProvider,
                jdbcSinkConfig.getJdbcConnectionConfig(),
                statementExecutorFactory);
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createCopyInBufferStatementExecutor(
            CopyManagerBatchStatementExecutor copyManagerBatchStatementExecutor) {
        return new BufferedBatchStatementExecutor(
                copyManagerBatchStatementExecutor, Function.identity());
    }

    private static CopyManagerBatchStatementExecutor createCopyInBatchStatementExecutor(
            JdbcDialect dialect,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema) {
        String columns =
                Arrays.stream(tableSchema.getFieldNames())
                        .map(dialect::quoteIdentifier)
                        .collect(Collectors.joining(",", "(", ")"));
        String copyInSql = String.format("COPY %s %s FROM STDIN WITH CSV", table, columns);
        return new CopyManagerBatchStatementExecutor(copyInSql, tableSchema);
    }

    private JdbcBatchStatementExecutor<SeaTunnelRow> createSimpleBufferedExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema) {
        String insertSQL =
                dialect.getInsertIntoStatement(database, table, tableSchema.getFieldNames());
        return createSimpleBufferedExecutor(
                insertSQL, tableSchema, databaseTableSchema, dialect.getRowConverter());
    }

    private JdbcBatchStatementExecutor<SeaTunnelRow> createSimpleBufferedExecutor(
            String sql,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            JdbcRowConverter rowConverter) {
        if (useErrorStrategy()) {
            return new ErrorStrategyBatchStatementExecutor(
                    connection ->
                            FieldNamedPreparedStatement.prepareStatement(
                                    connection, sql, tableSchema.getFieldNames()),
                    connection ->
                            FieldNamedPreparedStatement.prepareStatement(
                                    connection, sql, tableSchema.getFieldNames()),
                    tableSchema,
                    rowConverter,
                    getStatementSql(sql),
                    dialect.getUniquenessSql(),
                    databaseTableSchema,
                    jdbcSinkConfig.getJdbcConnectionConfig().getInsertErrorStrategy(),
                    jdbcSinkConfig.getJdbcConnectionConfig().getPkStrategy(),
                    jdbcSinkConfig.getJdbcConnectionConfig().getUrl());
        }
        JdbcBatchStatementExecutor<SeaTunnelRow> simpleRowExecutor =
                createSimpleExecutor(sql, tableSchema, databaseTableSchema, rowConverter);
        return new BufferedBatchStatementExecutor(simpleRowExecutor, Function.identity());
    }

    private JdbcBatchStatementExecutor<SeaTunnelRow> createDeleteConflictExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            String[] pkNames,
            boolean enableUpsert,
            boolean isPrimaryKeyUpdated,
            boolean supportUpsertByInsertOnly) {
        int[] pkFields;
        String[] fieldNames = tableSchema.getFieldNames();
        // 忽略大小写比对在目标的索引位
        pkFields =
                Arrays.stream(pkNames)
                        .mapToInt(tableSchema.toPhysicalRowDataType()::indexOf)
                        .toArray();
        pkNames = new String[pkFields.length];
        // 根据索引位置取出目标的真实字段写法
        for (int i = 0; i < pkFields.length; i++) {
            int index = pkFields[i];
            pkNames[i] = fieldNames[index];
        }
        TableSchema pkSchema =
                TableSchema.builder()
                        .columns(
                                Arrays.stream(pkFields)
                                        .mapToObj(
                                                (IntFunction<Column>) tableSchema.getColumns()::get)
                                        .collect(Collectors.toList()))
                        .build();
        Function<SeaTunnelRow, SeaTunnelRow> keyExtractor = createKeyExtractor(pkFields);
        String[] finalPkNames = pkNames;
        return new DeleteBatchStatementExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection,
                                dialect.getDeleteStatement(database, table, finalPkNames),
                                finalPkNames),
                pkSchema,
                keyExtractor,
                databaseTableSchema,
                dialect.getRowConverter());
    }

    public JdbcBatchStatementExecutor<SeaTunnelRow> createUpdateOnlyConflictExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            String[] pkNames,
            boolean enableUpsert,
            boolean isPrimaryKeyUpdated,
            boolean supportUpsertByInsertOnly) {
        int[] pkFields;
        String[] fieldNames = tableSchema.getFieldNames();
        // 忽略大小写比对在目标的索引位
        pkFields =
                Arrays.stream(pkNames)
                        .mapToInt(tableSchema.toPhysicalRowDataType()::indexOf)
                        .toArray();
        pkNames = new String[pkFields.length];
        // 根据索引位置取出目标的真实字段写法
        for (int i = 0; i < pkFields.length; i++) {
            int index = pkFields[i];
            pkNames[i] = fieldNames[index];
        }
        TableSchema pkSchema =
                TableSchema.builder()
                        .columns(
                                Arrays.stream(pkFields)
                                        .mapToObj(
                                                (IntFunction<Column>) tableSchema.getColumns()::get)
                                        .collect(Collectors.toList()))
                        .build();
        Function<SeaTunnelRow, SeaTunnelRow> keyExtractor = createKeyExtractor(pkFields);
        String updateSql =
                dialect.getUpdateStatement(
                        database, table, fieldNames, pkNames, jdbcSinkConfig.isPrimaryKeyUpdated());
        return createSimpleBufferedExecutor(
                updateSql, tableSchema, databaseTableSchema, dialect.getRowConverter());
    }

    public JdbcBatchStatementExecutor<SeaTunnelRow> createDeleteFirstInsertAfterExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            String[] pkNames,
            boolean enableUpsert,
            boolean isPrimaryKeyUpdated,
            boolean supportUpsertByInsertOnly) {
        int[] pkFields;
        String[] fieldNames = tableSchema.getFieldNames();
        // 忽略大小写比对在目标的索引位
        pkFields =
                Arrays.stream(pkNames)
                        .mapToInt(tableSchema.toPhysicalRowDataType()::indexOf)
                        .toArray();
        pkNames = new String[pkFields.length];
        // 根据索引位置取出目标的真实字段写法
        for (int i = 0; i < pkFields.length; i++) {
            int index = pkFields[i];
            pkNames[i] = fieldNames[index];
        }
        TableSchema deleteSchema =
                TableSchema.builder()
                        .columns(
                                Arrays.stream(pkFields)
                                        .mapToObj(
                                                (IntFunction<Column>) tableSchema.getColumns()::get)
                                        .collect(Collectors.toList()))
                        .build();
        Function<SeaTunnelRow, SeaTunnelRow> keyExtractor = createKeyExtractor(pkFields);
        String insertSQL =
                dialect.getInsertIntoStatement(database, table, tableSchema.getFieldNames());
        String deleteSQL = dialect.getDeleteStatement(database, table, pkNames);
        String existSQL = dialect.getRowExistsStatement(database, table, pkNames);
        if (pkNames.length == 1) {
            deleteSQL = dialect.getDeleteStatement(database, table, pkNames);
        }
        if (jdbcSinkConfig.isDeleteUseTemptable()) {
            String tempTableName = jdbcSinkConfig.getTempTableName();
            String insertTempTableSql =
                    dialect.getInsertIntoStatement(database, tempTableName, pkNames);
            String deleteUseTempTableSql =
                    dialect.getDeleteUseTempTableStatement(database, table, tempTableName, pkNames);
            String deleteTempTableSql =
                    dialect.getDeleteStatement(database, tempTableName, pkNames);
            return createDeleteAndInsertFromTempTableBufferedExecutor(
                    insertSQL,
                    insertTempTableSql,
                    deleteUseTempTableSql,
                    deleteTempTableSql,
                    tableSchema,
                    deleteSchema,
                    keyExtractor,
                    databaseTableSchema,
                    dialect.getRowConverter());
        }
        return createDeleteAndInsertBufferedExecutor(
                existSQL,
                insertSQL,
                deleteSQL,
                tableSchema,
                deleteSchema,
                keyExtractor,
                databaseTableSchema,
                dialect.getRowConverter());
    }

    public static JdbcBatchStatementExecutor<SeaTunnelRow> createHiveBufferedExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            JdbcSinkConfig jdbcSinkConfig) {
        String insertSQL =
                dialect.getInsertIntoStatement(database, table, tableSchema.getFieldNames());
        return createHiveBufferedExecutor(
                insertSQL, tableSchema, dialect.getRowConverter(), jdbcSinkConfig);
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createHiveBufferedExecutor(
            String sql,
            TableSchema tableSchema,
            JdbcRowConverter rowConverter,
            JdbcSinkConfig jdbcSinkConfig) {
        return new HiveBatchStatementExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, sql, tableSchema.getFieldNames()),
                tableSchema,
                rowConverter,
                Function.identity(),
                jdbcSinkConfig);
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createDeleteAndInsertBufferedExecutor(
            String existSql,
            String insertSql,
            String deleteSql,
            TableSchema tableSchema,
            TableSchema deleteSchema,
            Function<SeaTunnelRow, SeaTunnelRow> keyExtractor,
            TableSchema databaseTableSchema,
            JdbcRowConverter rowConverter) {
        return new DeleteAndInsertBufferedExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, existSql, deleteSchema.getFieldNames()),
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, insertSql, tableSchema.getFieldNames()),
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, deleteSql, deleteSchema.getFieldNames()),
                tableSchema,
                deleteSchema,
                databaseTableSchema,
                keyExtractor,
                rowConverter,
                Function.identity());
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow>
            createDeleteAndInsertFromTempTableBufferedExecutor(
                    String insertSql,
                    String insertTempSql,
                    String deleteSql,
                    String deleteTempSql,
                    TableSchema tableSchema,
                    TableSchema deleteSchema,
                    Function<SeaTunnelRow, SeaTunnelRow> keyExtractor,
                    TableSchema databaseTableSchema,
                    JdbcRowConverter rowConverter) {
        return new DeleteAndInsertBufferedUseTempTableExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, insertSql, tableSchema.getFieldNames()),
                connection -> connection.prepareStatement(deleteSql),
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, insertTempSql, deleteSchema.getFieldNames()),
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, deleteTempSql, deleteSchema.getFieldNames()),
                tableSchema,
                deleteSchema,
                keyExtractor,
                databaseTableSchema,
                rowConverter,
                Function.identity());
    }

    private JdbcBatchStatementExecutor<SeaTunnelRow> createUpsertBufferedExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            String[] pkNames,
            boolean enableUpsert,
            boolean isPrimaryKeyUpdated,
            boolean supportUpsertByInsertOnly) {
        int[] pkFields;
        String[] fieldNames = tableSchema.getFieldNames();
        // 忽略大小写比对在目标的索引位
        pkFields =
                Arrays.stream(pkNames)
                        .mapToInt(tableSchema.toPhysicalRowDataType()::indexOf)
                        .toArray();
        pkNames = new String[pkFields.length];
        // 根据索引位置取出目标的真实字段写法
        for (int i = 0; i < pkFields.length; i++) {
            int index = pkFields[i];
            pkNames[i] = fieldNames[index];
        }
        TableSchema pkSchema =
                TableSchema.builder()
                        .columns(
                                Arrays.stream(pkFields)
                                        .mapToObj(
                                                (IntFunction<Column>) tableSchema.getColumns()::get)
                                        .collect(Collectors.toList()))
                        .build();

        Function<SeaTunnelRow, SeaTunnelRow> keyExtractor = createKeyExtractor(pkFields);
        JdbcBatchStatementExecutor<SeaTunnelRow> deleteExecutor =
                createDeleteExecutor(
                        dialect, database, table, pkNames, pkSchema, databaseTableSchema);
        JdbcBatchStatementExecutor<SeaTunnelRow> upsertExecutor =
                createUpsertExecutor(
                        dialect,
                        database,
                        table,
                        tableSchema,
                        pkNames,
                        pkSchema,
                        databaseTableSchema,
                        keyExtractor,
                        enableUpsert,
                        isPrimaryKeyUpdated,
                        supportUpsertByInsertOnly);
        return new BufferReducedBatchStatementExecutor(
                upsertExecutor, deleteExecutor, keyExtractor, Function.identity());
    }

    private JdbcBatchStatementExecutor<SeaTunnelRow> createUpsertExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            String[] pkNames,
            TableSchema pkTableSchema,
            TableSchema databaseTableSchema,
            Function<SeaTunnelRow, SeaTunnelRow> keyExtractor,
            boolean enableUpsert,
            boolean isPrimaryKeyUpdated,
            boolean supportUpsertByInsertOnly) {
        if (supportUpsertByInsertOnly) {
            return createInsertOnlyExecutor(
                    dialect, database, table, tableSchema, databaseTableSchema);
        }
        if (enableUpsert) {
            Optional<String> upsertSQL =
                    dialect.getUpsertStatement(
                            database, table, tableSchema.getFieldNames(), pkNames);
            if (upsertSQL.isPresent()) {
                if (useErrorStrategy()) {
                    return new ErrorStrategyBatchStatementExecutor(
                            connection ->
                                    FieldNamedPreparedStatement.prepareStatement(
                                            connection,
                                            upsertSQL.get(),
                                            tableSchema.getFieldNames()),
                            connection ->
                                    FieldNamedPreparedStatement.prepareStatement(
                                            connection,
                                            upsertSQL.get(),
                                            tableSchema.getFieldNames()),
                            tableSchema,
                            dialect.getRowConverter(),
                            getStatementSql(upsertSQL.get()),
                            dialect.getUniquenessSql(),
                            databaseTableSchema,
                            jdbcSinkConfig.getJdbcConnectionConfig().getInsertErrorStrategy(),
                            jdbcSinkConfig.getJdbcConnectionConfig().getPkStrategy(),
                            jdbcSinkConfig.getJdbcConnectionConfig().getUrl());
                }
                return createSimpleExecutor(
                        upsertSQL.get(),
                        tableSchema,
                        databaseTableSchema,
                        dialect.getRowConverter());
            }
            return createInsertOrUpdateByQueryExecutor(
                    dialect,
                    database,
                    table,
                    tableSchema,
                    pkNames,
                    databaseTableSchema,
                    pkTableSchema,
                    keyExtractor,
                    isPrimaryKeyUpdated);
        }
        return createInsertOrUpdateExecutor(
                dialect,
                database,
                table,
                tableSchema,
                databaseTableSchema,
                pkNames,
                isPrimaryKeyUpdated);
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createInsertOnlyExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema) {

        return new SimpleBatchStatementExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection,
                                dialect.getInsertIntoStatement(
                                        database, table, tableSchema.getFieldNames()),
                                tableSchema.getFieldNames()),
                tableSchema,
                databaseTableSchema,
                dialect.getRowConverter());
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createInsertOrUpdateExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            String[] pkNames,
            boolean isPrimaryKeyUpdated) {

        return new InsertOrUpdateBatchStatementExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection,
                                dialect.getInsertIntoStatement(
                                        database, table, tableSchema.getFieldNames()),
                                tableSchema.getFieldNames()),
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection,
                                dialect.getUpdateStatement(
                                        database,
                                        table,
                                        tableSchema.getFieldNames(),
                                        pkNames,
                                        isPrimaryKeyUpdated),
                                tableSchema.getFieldNames()),
                tableSchema,
                databaseTableSchema,
                dialect.getRowConverter());
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createInsertOrUpdateByQueryExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            TableSchema tableSchema,
            String[] pkNames,
            TableSchema databaseTableSchema,
            TableSchema pkTableSchema,
            Function<SeaTunnelRow, SeaTunnelRow> keyExtractor,
            boolean isPrimaryKeyUpdated) {
        return new InsertOrUpdateBatchStatementExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection,
                                dialect.getRowExistsStatement(database, table, pkNames),
                                pkNames),
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection,
                                dialect.getInsertIntoStatement(
                                        database, table, tableSchema.getFieldNames()),
                                tableSchema.getFieldNames()),
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection,
                                dialect.getUpdateStatement(
                                        database,
                                        table,
                                        tableSchema.getFieldNames(),
                                        pkNames,
                                        isPrimaryKeyUpdated),
                                tableSchema.getFieldNames()),
                pkTableSchema,
                keyExtractor,
                tableSchema,
                databaseTableSchema,
                dialect.getRowConverter());
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createDeleteExecutor(
            JdbcDialect dialect,
            String database,
            String table,
            String[] pkNames,
            TableSchema pkTableSchema,
            TableSchema databaseTableSchema) {
        String deleteSQL = dialect.getDeleteStatement(database, table, pkNames);
        return createSimpleExecutor(
                deleteSQL, pkTableSchema, databaseTableSchema, dialect.getRowConverter());
    }

    private static JdbcBatchStatementExecutor<SeaTunnelRow> createSimpleExecutor(
            String sql,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            JdbcRowConverter rowConverter) {
        return new SimpleBatchStatementExecutor(
                connection ->
                        FieldNamedPreparedStatement.prepareStatement(
                                connection, sql, tableSchema.getFieldNames()),
                tableSchema,
                databaseTableSchema,
                rowConverter);
    }

    static Function<SeaTunnelRow, SeaTunnelRow> createKeyExtractor(int[] pkFields) {
        return row -> {
            Object[] fields = new Object[pkFields.length];
            for (int i = 0; i < pkFields.length; i++) {
                fields[i] = row.getField(pkFields[i]);
            }
            SeaTunnelRow newRow = new SeaTunnelRow(fields);
            newRow.setTableId(row.getTableId());
            return newRow;
        };
    }

    private boolean useErrorStrategy() {
        String pkStrategy = jdbcSinkConfig.getJdbcConnectionConfig().getPkStrategy();
        String insertErrorStrategy =
                jdbcSinkConfig.getJdbcConnectionConfig().getInsertErrorStrategy();
        if (StringUtils.equals("continue", insertErrorStrategy)
                || StringUtils.equals("continue", pkStrategy)) {
            // 20250821 注释掉检查、抛异常的方法
            // checkParamWhenUseErrorStrategy();
            return true;
        }
        return false;
    }

    // when use error strategy ,must check param
    private void checkParamWhenUseErrorStrategy() {
        boolean exactlyOnce = jdbcSinkConfig.isExactlyOnce();
        checkState(!exactlyOnce, "when use error strategy , param exactly_once must is false");
        boolean autoCommit = jdbcSinkConfig.getJdbcConnectionConfig().autoCommit;
        checkState(!autoCommit, "when use error strategy , param auto_commit must is false");
    }
}
