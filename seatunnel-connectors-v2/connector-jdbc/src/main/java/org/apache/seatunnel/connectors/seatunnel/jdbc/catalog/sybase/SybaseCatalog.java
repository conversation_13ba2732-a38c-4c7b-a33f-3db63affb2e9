package org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.sybase;

import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
import org.apache.seatunnel.api.table.catalog.TablePath;
import org.apache.seatunnel.api.table.catalog.exception.CatalogException;
import org.apache.seatunnel.api.table.catalog.exception.DatabaseNotExistException;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.common.utils.JdbcUrlUtil;
import org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.AbstractJdbcCatalog;
import org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.utils.CatalogUtils;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.sybase.SybaseTypeMapper;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class SybaseCatalog extends AbstractJdbcCatalog {

    private static final SybaseDataTypeConvertor DATA_TYPE_CONVERTOR =
            new SybaseDataTypeConvertor();

    private static final String SELECT_COLUMNS_SQL_TEMPLATE =
            "SELECT obj.name AS table_name,\n"
                    + "       col.name AS column_name,\n"
                    + "       col.colid AS column_id,\n"
                    + "       t.name AS type,\n"
                    + "       col.length AS max_length,\n"
                    + "       col.prec AS precision_v,\n"
                    + "       col.scale AS scale,\n"
                    + "       col.status & 8 AS is_nullable, \n"
                    + "       def.text AS default_value\n"
                    + "FROM sysobjects obj\n"
                    + "    INNER JOIN syscolumns col ON obj.id = col.id\n"
                    + "    LEFT JOIN systypes t ON col.usertype = t.usertype\n"
                    + "    LEFT JOIN syscomments def ON col.cdefault = def.id\n"
                    + "WHERE obj.type = 'U' \n"
                    + "  AND user_name(obj.uid) = '%s' %s"
                    + " ORDER BY obj.name, col.colid";

    public SybaseCatalog(
            String catalogName,
            String username,
            String pwd,
            JdbcUrlUtil.UrlInfo urlInfo,
            String defaultSchema,
            String driverClass) {
        super(catalogName, username, pwd, urlInfo, defaultSchema, driverClass);
    }

    protected Connection getConnection(String url) {
        if (connectionMap.containsKey(url)) {
            return connectionMap.get(url);
        }
        try {
            Class.forName(driverClass);
            Connection connection = DriverManager.getConnection(url, username, pwd);
            connectionMap.put(url, connection);
            return connection;
        } catch (SQLException | ClassNotFoundException e) {
            throw new CatalogException(String.format("Failed connecting to %s via JDBC.", url), e);
        }
    }

    @Override
    protected String getListDatabaseSql() {
        return "SELECT name FROM master..sysdatabases";
    }

    @Override
    protected String getListTableSql(String databaseName) {
        return String.format(
                "SELECT user_name(uid) AS TABLE_SCHEMA,\n"
                        + " name AS TABLE_NAME \n"
                        + "FROM %s..sysobjects WHERE type = 'U'",
                databaseName);
    }

    @Override
    protected String getSelectColumnsSql(TablePath tablePath) {
        String tableSql =
                StringUtils.isNotEmpty(tablePath.getTableName())
                        ? "AND obj.name = '" + tablePath.getTableName() + "'"
                        : "";
        return String.format(SELECT_COLUMNS_SQL_TEMPLATE, tablePath.getSchemaName(), tableSql);
    }

    @Override
    protected Column buildColumn(ResultSet resultSet) throws SQLException {
        String columnName = resultSet.getString("column_name").trim();
        String sourceType = resultSet.getString("type").trim();
        //        String typeName = resultSet.getString("DATA_TYPE").toUpperCase();
        int precision = resultSet.getInt("precision_v");
        int scale = resultSet.getInt("scale");
        long columnLength = resultSet.getLong("max_length");
        SeaTunnelDataType<?> type = fromJdbcType(columnName, sourceType, precision, scale);
        // String comment = resultSet.getString("comment");
        Object defaultValue = resultSet.getObject("default_value");
        if (defaultValue != null) {
            defaultValue =
                    defaultValue.toString().replace("(", "").replace("'", "").replace(")", "");
        }
        boolean isNullable = resultSet.getBoolean("is_nullable");
        long bitLen = 0;
        StringBuilder sb = new StringBuilder(sourceType);
        Pair<SybaseType, Map<String, Object>> parse = SybaseType.parse(sourceType);
        switch (parse.getLeft()) {
            case IQ_LONG_BINARY:
            case BINARY:
            case VARBINARY:
                // Uniform conversion to bits
                if (columnLength != -1) {
                    bitLen = columnLength * 4 * 8;
                    sourceType = sb.append("(").append(columnLength).append(")").toString();
                } else {
                    sourceType = sb.append("(").append("max").append(")").toString();
                    bitLen = columnLength;
                }
                break;
            case TIMESTAMP:
                bitLen = columnLength << 3;
                break;
            case IQ_UNIQUEIDENTIFIER:
            case VARCHAR:
            case NCHAR:
            case NVARCHAR:
            case CHAR:
                if (columnLength != -1) {
                    sourceType = sb.append("(").append(columnLength).append(")").toString();
                } else {
                    sourceType = sb.append("(").append("max").append(")").toString();
                }
                break;
            case IQ_DOUBLE:
            case DECIMAL:
            case NUMERIC:
                sourceType =
                        sb.append("(")
                                .append(precision)
                                .append(",")
                                .append(scale)
                                .append(")")
                                .toString();
                break;
            case TEXT:
            case LONGVARCHAR:
                columnLength = Integer.MAX_VALUE;
                break;
            case IMAGE:
                bitLen = Integer.MAX_VALUE * 8L;
                break;
            default:
                break;
        }
        return PhysicalColumn.of(
                columnName,
                type,
                0,
                isNullable,
                defaultValue,
                null,
                sourceType,
                false,
                false,
                bitLen,
                null,
                columnLength);
    }

    private SeaTunnelDataType<?> fromJdbcType(
            String columnName, String typeName, int precision, int scale) {
        Pair<SybaseType, Map<String, Object>> pair = SybaseType.parse(typeName);
        Map<String, Object> dataTypeProperties = new HashMap<>();
        dataTypeProperties.put(SybaseDataTypeConvertor.PRECISION, precision);
        dataTypeProperties.put(SybaseDataTypeConvertor.SCALE, scale);
        return DATA_TYPE_CONVERTOR.toSeaTunnelType(columnName, pair.getLeft(), dataTypeProperties);
    }

    @Override
    protected String getDropTableSql(TablePath tablePath) {
        return String.format("DROP TABLE %s", tablePath.getFullName());
    }

    @Override
    protected String getCreateDatabaseSql(String databaseName) {
        return String.format("CREATE DATABASE %s", databaseName);
    }

    @Override
    protected String getDropDatabaseSql(String databaseName) {
        return String.format("DROP DATABASE %s", databaseName);
    }

    @Override
    protected void dropDatabaseInternal(String databaseName) throws CatalogException {
        closeDatabaseConnection(databaseName);
        super.dropDatabaseInternal(databaseName);
    }

    @Override
    protected String getUrlFromDatabaseName(String databaseName) {
        return defaultUrl;
    }

    @Override
    public boolean tableExists(TablePath tablePath) throws CatalogException {
        try {
            if (StringUtils.isNotBlank(tablePath.getDatabaseName())) {
                return databaseExists(tablePath.getDatabaseName())
                        && listTables(tablePath.getDatabaseName())
                                .contains(tablePath.getSchemaAndTableName());
            }
            return listTables(defaultDatabase).contains(tablePath.getSchemaAndTableName());
        } catch (DatabaseNotExistException e) {
            return false;
        }
    }

    @Override
    public CatalogTable getTable(String sqlQuery) throws SQLException {
        Connection defaultConnection = getConnection(defaultUrl);
        return CatalogUtils.getCatalogTable(defaultConnection, sqlQuery, new SybaseTypeMapper());
    }

    @Override
    public String getExistDataSql(TablePath tablePath) {
        return String.format("select TOP 1 * from %s ", tablePath.getFullNameWithQuoted("[", "]"));
    }

    @Override
    protected String getTruncateTableSql(TablePath tablePath) throws CatalogException {
        return String.format("TRUNCATE TABLE  %s", tablePath.getFullNameWithQuoted("[", "]"));
    }
}
