package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;
import org.apache.seatunnel.connectors.seatunnel.jdbc.utils.SqlExceptionUtils;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
public class ErrorStrategyBatchStatementExecutor
        implements JdbcBatchStatementExecutor<SeaTunnelRow> {

    @NonNull private final StatementFactory insertOrUpsertStatementFactory;
    @NonNull private final StatementFactory singleStatementFactory;
    @NonNull private final TableSchema valueTableSchema;
    @NonNull private final JdbcRowConverter rowConverter;
    @NonNull private final String insertOrUpsertSql;
    @NonNull private final Optional<String> uniquenessSql;
    private final TableSchema databaseTableSchema;
    private final String insertErrorStrategy;
    private final String pkStrategy;
    private final String jdbcUrl;
    private final List<SeaTunnelRow> buffer = new ArrayList<>();

    private PreparedStatement singleStatement;
    private PreparedStatement insertOrUpsertStatement;

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        this.insertOrUpsertStatement = insertOrUpsertStatementFactory.createStatement(connection);
        this.singleStatement = singleStatementFactory.createStatement(connection);
    }

    @Override
    public void addToBatch(SeaTunnelRow record) throws SQLException {
        buffer.add(record);
    }

    @Override
    public void executeBatch() throws SQLException {
        doExecuteBatch();
    }

    protected synchronized void doExecuteBatch() throws SQLException {
        if (!buffer.isEmpty()) {
            for (SeaTunnelRow row : buffer) {
                try {
                    rowConverter.toExternal(
                            valueTableSchema, databaseTableSchema, row, insertOrUpsertStatement);
                    insertOrUpsertStatement.addBatch();
                } catch (SQLException e) {
                    if (insertErrorContinue()) {
                        log.error(
                                "SeaTunnelRow toExternal error, ignore this row.{}",
                                e.getMessage());
                    } else {
                        throw e;
                    }
                }
            }
            try {
                insertOrUpsertStatement.executeBatch();
                commit();
            } catch (SQLException e) {
                rollback();
                if (uniquenessSql.isPresent()) {
                    executeSingleWithComment(buffer);
                } else {
                    executeSingle(buffer);
                }
            } finally {
                insertOrUpsertStatement.clearBatch();
                buffer.clear();
            }
        }
    }

    @Override
    public void closeStatements() throws SQLException {
        try {
            if (!buffer.isEmpty()) {
                executeBatch();
                commit();
            }
        } finally {
            insertOrUpsertStatement.close();
            singleStatement.close();
        }
    }

    protected void executeSingle(List<SeaTunnelRow> buffer) throws SQLException {
        for (SeaTunnelRow seaTunnelRow : buffer) {
            try {
                rowConverter.toExternal(
                        valueTableSchema, databaseTableSchema, seaTunnelRow, singleStatement);
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
                continue;
            }
            try {
                singleStatement.execute();
                commit();
            } catch (SQLException e) {
                if (SqlExceptionUtils.isIgnoreSingleRecord(
                        pkStrategy, insertErrorStrategy, jdbcUrl, e)) {
                    // e.printStackTrace();
                    log.error("Ignore single record exception:{}", e.getMessage());
                    log.error("Ignore single record: {}", seaTunnelRow);
                    try {
                        singleStatement.clearBatch();
                        singleStatement.clearParameters();
                        rollback();
                    } catch (SQLException ex) {
                        log.error("Ignore single record exception when clearParameters", ex);
                    }
                } else {
                    throw e;
                }
            }
        }
    }

    protected void executeSingleWithComment(List<SeaTunnelRow> buffer) throws SQLException {
        for (SeaTunnelRow seaTunnelRow : buffer) {
            try {
                rowConverter.toExternal(
                        valueTableSchema, databaseTableSchema, seaTunnelRow, singleStatement);
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
                continue;
            }
            try {
                executeSingleWithComment(seaTunnelRow);
                commit();
            } catch (SQLException e) {
                if (SqlExceptionUtils.isIgnoreSingleRecord(
                        pkStrategy, insertErrorStrategy, jdbcUrl, e)) {
                    // e.printStackTrace();
                    log.error("Ignore single record exception:{}", e.getMessage());
                    log.error("Ignore single record: {}", seaTunnelRow);
                    try {
                        singleStatement.clearBatch();
                        singleStatement.clearParameters();
                        rollback();
                    } catch (SQLException ex) {
                        log.error("Ignore single record exception when clearParameters", ex);
                    }
                } else {
                    throw e;
                }
            }
        }
    }

    private boolean insertErrorContinue() throws SQLException {
        return insertErrorStrategy.equalsIgnoreCase("continue");
    }

    private void commit() throws SQLException {
        insertOrUpsertStatement.getConnection().commit();
    }

    private void rollback() throws SQLException {
        insertOrUpsertStatement.getConnection().rollback();
    }

    private void executeSingleWithComment(SeaTunnelRow record) throws SQLException {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String executeSql = insertOrUpsertSql + " " + String.format(uniquenessSql.get(), uuid);
        try (PreparedStatement statement =
                insertOrUpsertStatement.getConnection().prepareStatement(executeSql)) {
            rowConverter.toExternal(valueTableSchema, databaseTableSchema, record, statement);
            statement.execute();
            commit();
        }
    }
}
