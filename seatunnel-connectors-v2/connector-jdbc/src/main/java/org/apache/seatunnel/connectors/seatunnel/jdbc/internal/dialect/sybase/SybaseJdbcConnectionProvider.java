package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.sybase;

import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcConnectionConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection.SimpleJdbcConnectionProvider;

import lombok.NonNull;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.SQLException;
import java.util.Properties;

public class SybaseJdbcConnectionProvider extends SimpleJdbcConnectionProvider {

    public SybaseJdbcConnectionProvider(@NonNull JdbcConnectionConfig jdbcConfig) {
        super(jdbcConfig);
    }

    @Override
    public Connection getOrEstablishConnection() throws SQLException, ClassNotFoundException {
        if (isConnectionValid()) {
            return connection;
        }
        Driver driver = getLoadedDriver();
        Properties info = new Properties();
        /*info.put("db2.jcc.charsetDecoderEncoder", "3");  // 修复字符转换错误
        info.put("charsetEncoding", "UTF-8");           // 强制使用 UTF-8 编码
        info.put("db2.jcc.clobCharacterSet", "UTF-8");  // 确保 CLOB 使用 UTF-8*/
        if (jdbcConfig.getUrl().startsWith("jdbc:db2")) {
            System.setProperty("db2.jcc.charsetDecoderEncoder", "3");
        }
        if (jdbcConfig.getUsername().isPresent()) {
            info.setProperty("user", jdbcConfig.getUsername().get());
        }
        if (jdbcConfig.getPassword().isPresent()) {
            info.setProperty("password", jdbcConfig.getPassword().get());
        }
        info.putAll(jdbcConfig.getProperties());
        connection = driver.connect(jdbcConfig.getUrl(), info);

        if (connection == null) {
            // Throw same exception as DriverManager.getConnection when no driver found to match
            // caller expectation.
            throw new JdbcConnectorException(
                    JdbcConnectorErrorCode.NO_SUITABLE_DRIVER,
                    "No suitable driver found for " + jdbcConfig.getUrl());
        }
        connection.setAutoCommit(jdbcConfig.isAutoCommit());
        return connection;
    }
}
