/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.split;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

import static org.apache.seatunnel.shade.com.google.common.base.Preconditions.checkArgument;
import static org.apache.seatunnel.shade.com.google.common.base.Preconditions.checkState;

/**
 * This query parameters generator is an helper class to parameterize from/to queries on a numeric
 * column. The generated array of from/to values will be equally sized to fetchSize (apart from the
 * last one), ranging from minVal up to maxVal.
 *
 * <p>For example, if there's a table <CODE>BOOKS</CODE> with a numeric PK <CODE>id</CODE>, using a
 * query like:
 *
 * <PRE>
 * SELECT * FROM BOOKS WHERE id BETWEEN ? AND ?
 * </PRE>
 *
 * <p>You can take advantage of this class to automatically generate the parameters of the BETWEEN
 * clause, based on the passed constructor parameters.
 */
public class JdbcNumericBetweenParametersProvider implements JdbcParameterValuesProvider {

    private final BigDecimal minVal;
    private final BigDecimal maxVal;

    private long batchSize;
    private int batchNum;

    /**
     * NumericBetweenParametersProviderJdbc constructor.
     *
     * @param minVal the lower bound of the produced "from" values
     * @param maxVal the upper bound of the produced "to" values
     */
    public JdbcNumericBetweenParametersProvider(BigDecimal minVal, BigDecimal maxVal) {
        checkArgument(minVal.compareTo(maxVal) <= 0, "minVal must not be larger than maxVal");
        this.minVal = minVal;
        this.maxVal = maxVal;
    }

    /**
     * NumericBetweenParametersProviderJdbc constructor.
     *
     * @param fetchSize the max distance between the produced from/to pairs
     * @param minVal the lower bound of the produced "from" values
     * @param maxVal the upper bound of the produced "to" values
     */
    public JdbcNumericBetweenParametersProvider(
            long fetchSize, BigDecimal minVal, BigDecimal maxVal) {
        checkArgument(minVal.compareTo(maxVal) <= 0, "minVal must not be larger than maxVal");
        this.minVal = minVal;
        this.maxVal = maxVal;
        ofBatchSize(fetchSize);
    }

    public JdbcNumericBetweenParametersProvider ofBatchSize(long batchSize) {
        checkArgument(batchSize > 0, "Batch size must be positive");

        BigDecimal maxElemCount = (maxVal.subtract(minVal)).add(BigDecimal.valueOf(1));
        if (BigDecimal.valueOf(batchSize).compareTo(maxElemCount) > 0) {
            batchSize = maxElemCount.longValue();
        }
        this.batchSize = batchSize;
        this.batchNum =
                new Double(
                                Math.ceil(
                                        (maxElemCount.divide(BigDecimal.valueOf(batchSize)))
                                                .doubleValue()))
                        .intValue();
        return this;
    }

    public JdbcNumericBetweenParametersProvider ofBatchNum(int batchNum) {
        checkArgument(batchNum > 0, "Batch number must be positive");

        BigDecimal maxElemCount = (maxVal.subtract(minVal)).add(BigDecimal.valueOf(1));
        if (BigDecimal.valueOf(batchNum).compareTo(maxElemCount) > 0) {
            batchNum = maxElemCount.intValue();
        }
        this.batchNum = batchNum;
        // For the presence of a decimal we take the integer up
        this.batchSize =
                (maxElemCount.divide(BigDecimal.valueOf(batchNum), 2, RoundingMode.HALF_UP))
                        .setScale(0, RoundingMode.CEILING)
                        .longValue();
        return this;
    }
    /**
     * 分片示例（min=1.00, max=2.00, splitCount=3, scale=2） 理论步长 = (2.00-1.00)/3 = 0.333333... delta =
     * 0.01（因scale=2）
     *
     * <p>分片1： currentStart = 1.00 currentEnd = (1.00 + 0.333333) → 1.333333 → 调整为1.33 → 1.33 - 0.01
     * = 1.32 实际范围：[1.00, 1.32] → 转换为查询条件 WHERE field >= 1.00 AND field <= 1.32
     *
     * <p>分片2： currentStart = 1.32 + 0.01 = 1.33 currentEnd = 1.33 + 0.333333 → 1.663333 → 调整为1.66 →
     * 1.66 - 0.01 = 1.65 实际范围：[1.33, 1.65] → 查询条件 WHERE field >= 1.33 AND field <= 1.65
     *
     * <p>分片3（最后一个）： currentStart = 1.65 + 0.01 = 1.66 currentEnd = 2.00（强制对齐） 实际范围：[1.66, 2.00] →
     * 查询条件 WHERE field >= 1.66 AND field <= 2.00
     *
     * @param scale
     * @param batchNum
     * @return
     */
    public Serializable[][] calculateSplits(int scale, Integer batchNum) {
        // 1. 参数校验
        if (batchNum <= 0) {
            throw new IllegalArgumentException("分片数量必须大于0");
        }
        if (maxVal.compareTo(minVal) < 0) {
            throw new IllegalArgumentException("最大值必须大于等于最小值");
        }

        // 创建中间计算精度上下文（额外保留2位小数避免舍入误差）
        MathContext mc = new MathContext(scale + 2, RoundingMode.HALF_UP); // 保留额外精度用于中间计算
        // 将输入值统一调整为指定精度（四舍五入）
        BigDecimal minValAdjusted = minVal.setScale(scale, RoundingMode.HALF_UP);
        BigDecimal maxValAdjusted = maxVal.setScale(scale, RoundingMode.HALF_UP);

        // 计算总范围（max - min）
        BigDecimal range = maxValAdjusted.subtract(minValAdjusted, mc);
        // 计算理论步长 = 总范围 / 分片数（使用高精度计算）
        BigDecimal step = range.divide(new BigDecimal(batchNum), mc); // 动态计算步长
        BigDecimal delta = BigDecimal.ONE.movePointLeft(scale); // 例如 精度scale=2 → delta=0.01

        // 4. 生成分片边界
        Serializable[][] parameters = new Serializable[batchNum][2];
        BigDecimal currentStart = minValAdjusted;
        for (int i = 0; i < batchNum; i++) {
            BigDecimal currentEnd;
            if (i == batchNum - 1) {
                // 情况1：最后一个分片
                // 强制结束值对齐最大值（闭区间查询确保数据不遗漏）
                currentEnd = maxValAdjusted;
            } else {
                // 情况2：前N-1个分片
                // 计算理论结束值 = start + step，然后减去delta实现左闭右开
                currentEnd =
                        currentStart
                                .add(step, mc)
                                .setScale(scale, RoundingMode.HALF_UP)
                                .subtract(delta); // 减去 delta 避免重叠
            }
            parameters[i] = new BigDecimal[] {currentStart, currentEnd};
            currentStart = currentEnd.add(delta).setScale(scale, RoundingMode.HALF_UP); // 下一分片起点
        }

        return parameters;
    }

    @Override
    public Serializable[][] getParameterValues() {
        checkState(
                batchSize > 0,
                "Batch size and batch number must be positive. Have you called `ofBatchSize` or `ofBatchNum`?");

        BigDecimal maxElemCount = (maxVal.subtract(minVal)).add(BigDecimal.valueOf(1));
        BigDecimal bigBatchNum =
                maxElemCount
                        .subtract(BigDecimal.valueOf(batchSize - 1))
                        .multiply(BigDecimal.valueOf(batchNum));

        Serializable[][] parameters = new Serializable[batchNum][2];
        BigDecimal start = minVal;
        for (int i = 0; i < batchNum; i++) {
            BigDecimal end =
                    start.add(BigDecimal.valueOf(batchSize))
                            .subtract(BigDecimal.valueOf(1))
                            .subtract(
                                    BigDecimal.valueOf(i).compareTo(bigBatchNum) >= 0
                                            ? BigDecimal.ONE
                                            : BigDecimal.ZERO);
            parameters[i] = new BigDecimal[] {start, end};
            start = end.add(BigDecimal.valueOf(1));
        }
        return parameters;
    }
}
