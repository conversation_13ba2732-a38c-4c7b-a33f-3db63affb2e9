package org.apache.seatunnel.connectors.seatunnel.jdbc.catalog.sybase;

import org.apache.seatunnel.api.table.catalog.DataTypeConvertor;
import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.DecimalType;
import org.apache.seatunnel.api.table.type.LocalTimeType;
import org.apache.seatunnel.api.table.type.PrimitiveByteArrayType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SqlType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;

import org.apache.commons.lang3.tuple.Pair;

import com.google.auto.service.AutoService;
import lombok.NonNull;

import java.util.Map;

@AutoService(DataTypeConvertor.class)
public class SybaseDataTypeConvertor implements DataTypeConvertor<SybaseType> {
    public static final String PRECISION = "precision";
    public static final String SCALE = "scale";
    public static final Integer DEFAULT_PRECISION = 10;
    public static final Integer DEFAULT_SCALE = 0;

    @Override
    public SeaTunnelDataType<?> toSeaTunnelType(String field, @NonNull String connectorDataType) {
        Pair<SybaseType, Map<String, Object>> sybaseTypeMapPair =
                SybaseType.parse(connectorDataType);
        return toSeaTunnelType(field, sybaseTypeMapPair.getLeft(), sybaseTypeMapPair.getRight());
    }

    @Override
    public SeaTunnelDataType<?> toSeaTunnelType(
            String field,
            @NonNull SybaseType connectorDataType,
            Map<String, Object> dataTypeProperties) {
        switch (connectorDataType) {
            case BIT:
                return BasicType.BOOLEAN_TYPE;
            case TINYINT:
            case SMALLINT:
                return BasicType.SHORT_TYPE;
            case INTEGER:
            case INT:
                return BasicType.INT_TYPE;
            case BIGINT:
                return BasicType.LONG_TYPE;
            case IQ_DOUBLE:
            case DECIMAL:
            case NUMERIC:
            case MONEY:
            case SMALLMONEY:
                int precision = (int) dataTypeProperties.getOrDefault(PRECISION, DEFAULT_PRECISION);
                int scale = (int) dataTypeProperties.getOrDefault(SCALE, DEFAULT_SCALE);
                return new DecimalType(precision, scale);
            case REAL:
                return BasicType.FLOAT_TYPE;
            case FLOAT:
            case DOUBLE_PRECISION:
                return BasicType.DOUBLE_TYPE;
            case CHAR:
            case NCHAR:
            case UNICHAR:
            case VARCHAR:
            case NVARCHAR:
            case UNIVARCHAR:
            case SYSNAME:
            case LONGSYSNAME:
            case TEXT:
            case LONGVARCHAR:
            case IQ_UNIQUEIDENTIFIER:
                return BasicType.STRING_TYPE;
            case DATE:
                return LocalTimeType.LOCAL_DATE_TYPE;
            case TIME:
            case BIGTIME:
                return LocalTimeType.LOCAL_TIME_TYPE;
            case DATETIME:
            case SMALLDATETIME:
            case BIGDATETIME:
                return LocalTimeType.LOCAL_DATE_TIME_TYPE;
            case TIMESTAMP:
            case BINARY:
            case VARBINARY:
            case IMAGE:
            case IQ_LONG_BINARY:
                return PrimitiveByteArrayType.INSTANCE;
            case UNKNOWN:
            default:
                throw CommonError.convertToSeaTunnelTypeError(
                        DatabaseIdentifier.SYBASE, connectorDataType.toString(), field);
        }
    }

    @Override
    public SybaseType toConnectorType(
            String field,
            SeaTunnelDataType<?> seaTunnelDataType,
            Map<String, Object> dataTypeProperties) {
        SqlType sqlType = seaTunnelDataType.getSqlType();
        switch (sqlType) {
            case STRING:
                return SybaseType.VARCHAR;
            case BOOLEAN:
                return SybaseType.BIT;
            case TINYINT:
                return SybaseType.TINYINT;
            case SMALLINT:
                return SybaseType.SMALLINT;
            case INT:
                return SybaseType.INTEGER;
            case BIGINT:
                return SybaseType.BIGINT;
            case FLOAT:
                return SybaseType.REAL;
            case DOUBLE:
                return SybaseType.FLOAT;
            case DECIMAL:
                return SybaseType.DECIMAL;
            case BYTES:
                return SybaseType.BINARY;
            case DATE:
                return SybaseType.DATE;
            case TIME:
                return SybaseType.TIME;
            case TIMESTAMP:
                return SybaseType.DATETIME;
            default:
                throw CommonError.convertToConnectorTypeError(
                        DatabaseIdentifier.SYBASE,
                        seaTunnelDataType.getSqlType().toString(),
                        field);
        }
    }

    @Override
    public String getIdentity() {
        return DatabaseIdentifier.SYBASE;
    }
}
