package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.informix;

import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialect;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialectFactory;

import com.google.auto.service.AutoService;

/**
 * <AUTHOR>
 * @date 2024/11/20
 */
@AutoService(JdbcDialectFactory.class)
public class InformixDialectFactory implements JdbcDialectFactory {
    @Override
    public boolean acceptsURL(String url) {
        return url.startsWith("jdbc:informix-sqli:");
    }

    @Override
    public JdbcDialect create() {
        return new InformixDialect();
    }
}
