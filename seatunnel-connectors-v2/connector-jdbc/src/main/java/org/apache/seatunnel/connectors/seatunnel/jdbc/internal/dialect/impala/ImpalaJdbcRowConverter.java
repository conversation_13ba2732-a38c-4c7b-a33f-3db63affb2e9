package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.impala;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.table.type.SqlType;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.AbstractJdbcRowConverter;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/6/15
 */
public class ImpalaJdbcRowConverter extends AbstractJdbcRowConverter {
    @Override
    public String converterName() {
        return DatabaseIdentifier.IMPALA;
    }

    @Override
    public PreparedStatement toExternal(
            TableSchema tableSchema, SeaTunnelRow row, PreparedStatement statement)
            throws SQLException {
        SeaTunnelRowType rowType = tableSchema.toPhysicalRowDataType();
        for (int fieldIndex = 0; fieldIndex < rowType.getTotalFields(); fieldIndex++) {
            SeaTunnelDataType<?> seaTunnelDataType = rowType.getFieldType(fieldIndex);
            int statementIndex = fieldIndex + 1;
            Object fieldValue = row.getField(fieldIndex);
            if ((SqlType.DATE == seaTunnelDataType.getSqlType()
                            || SqlType.TIMESTAMP == seaTunnelDataType.getSqlType()
                            || SqlType.TIMESTAMP == seaTunnelDataType.getSqlType())
                    && null == fieldValue) {
                statement.setObject(statementIndex, null);
                continue;
            }
            if (fieldValue == null) {
                statement.setString(statementIndex, null);
                continue;
            }
            SqlType sqlType = seaTunnelDataType.getSqlType();
            switch (sqlType) {
                case STRING:
                    statement.setString(statementIndex, String.valueOf(row.getField(fieldIndex)));
                    break;
                case BOOLEAN:
                    statement.setBoolean(statementIndex, (Boolean) row.getField(fieldIndex));
                    break;
                case TINYINT:
                    statement.setByte(statementIndex, (Byte) row.getField(fieldIndex));
                    break;
                case SMALLINT:
                    statement.setShort(statementIndex, (Short) row.getField(fieldIndex));
                    break;
                case INT:
                    statement.setInt(
                            statementIndex,
                            Integer.parseInt(String.valueOf(row.getField(fieldIndex))));
                    break;
                case BIGINT:
                    statement.setLong(statementIndex, (Long) row.getField(fieldIndex));
                    break;
                case FLOAT:
                    statement.setFloat(statementIndex, (Float) row.getField(fieldIndex));
                    break;
                case DOUBLE:
                    statement.setDouble(statementIndex, (Double) row.getField(fieldIndex));
                    break;
                case DECIMAL:
                    statement.setBigDecimal(statementIndex, (BigDecimal) row.getField(fieldIndex));
                    break;
                case DATE:
                    /* LocalDate localDate = (LocalDate) row.getField(fieldIndex);
                    statement.setDate(statementIndex, java.sql.Date.valueOf(localDate));*/
                    statement.setObject(statementIndex, String.valueOf(row.getField(fieldIndex)));
                    break;
                case TIME:
                    LocalTime localTime = (LocalTime) row.getField(fieldIndex);
                    // 20250306 由于源端是文本的使用了time类型，这里使用java.sql.time.valueof的时候，会导致缺少精度
                    /*statement.setTime(
                    statementIndex,
                    new Time(
                            localTime.toNanoOfDay() / 1_000_000
                                    - TimeZone.getDefault().getRawOffset()));*/
                    statement.setObject(statementIndex, localTime, Types.TIME);
                    break;
                case TIMESTAMP:
                    LocalDateTime localDateTime = (LocalDateTime) row.getField(fieldIndex);
                    /* statement.setTimestamp(
                    statementIndex, java.sql.Timestamp.valueOf(localDateTime));*/
                    statement.setObject(statementIndex, localDateTime, Types.TIME);
                    break;
                case BYTES:
                    statement.setBytes(statementIndex, (byte[]) row.getField(fieldIndex));
                    break;
                case NULL:
                    statement.setNull(statementIndex, java.sql.Types.NULL);
                    break;
                case MAP:
                case ARRAY:
                case ROW:
                    Object field = row.getField(fieldIndex);
                    if (field instanceof Object[]) {
                        // 强制类型转换
                        Object[] intArray = (Object[]) field;
                        statement.setString(statementIndex, Arrays.toString((intArray)));
                    } else {
                        statement.setString(statementIndex, String.valueOf(field));
                    }

                    break;
                default:
                    throw new JdbcConnectorException(
                            CommonErrorCodeDeprecated.UNSUPPORTED_DATA_TYPE,
                            "Unexpected value: " + seaTunnelDataType);
            }
        }
        return statement;
    }
}
