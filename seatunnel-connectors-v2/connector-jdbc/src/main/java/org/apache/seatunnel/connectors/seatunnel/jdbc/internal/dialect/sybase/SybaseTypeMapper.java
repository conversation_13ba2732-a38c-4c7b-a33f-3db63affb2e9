/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.sybase;

import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.DecimalType;
import org.apache.seatunnel.api.table.type.LocalTimeType;
import org.apache.seatunnel.api.table.type.PrimitiveByteArrayType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialectTypeMapper;

import lombok.extern.slf4j.Slf4j;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Types;

@Slf4j
public class SybaseTypeMapper implements JdbcDialectTypeMapper {

    // ============================data types=====================

    private static final String SYBASE_UNKNOWN = "UNKNOWN";

    // -------------------------number----------------------------
    private static final String SYBASE_TINYINT = "TINYINT";
    private static final String SYBASE_SMALLINT = "SMALLINT";
    private static final String SYBASE_INT = "INT";
    private static final String SYBASE_INTEGER = "INTEGER";
    private static final String SYBASE_BIGINT = "BIGINT";
    private static final String SYBASE_DECIMAL = "DECIMAL";
    private static final String SYBASE_NUMERIC = "NUMERIC";
    private static final String SYBASE_MONEY = "MONEY";
    private static final String SYBASE_SMALLMONEY = "SMALLMONEY";
    private static final String SYBASE_FLOAT = "FLOAT";
    private static final String SYBASE_DOUBLE_PRECISION = "DOUBLE PRECISION";
    private static final String SYBASE_REAL = "REAL";

    // -------------------------string----------------------------
    private static final String SYBASE_CHAR = "CHAR";
    private static final String SYBASE_VARCHAR = "VARCHAR";
    private static final String SYBASE_NCHAR = "NCHAR";
    private static final String SYBASE_NVARCHAR = "NVARCHAR";
    private static final String SYBASE_UNICHAR = "UNICHAR";
    private static final String SYBASE_UNIVARCHAR = "UNIVARCHAR";
    private static final String SYBASE_SYSNAME = "SYSNAME";
    private static final String SYBASE_LONGSYSNAME = "LONGSYSNAME";
    private static final String SYBASE_TEXT = "TEXT";

    // ------------------------------time-------------------------
    private static final String SYBASE_DATE = "DATE";
    private static final String SYBASE_TIME = "TIME";
    private static final String SYBASE_DATETIME = "DATETIME";
    private static final String SYBASE_SMALLDATETIME = "SMALLDATETIME";
    private static final String SYBASE_BIGDATETIME = "BIGDATETIME";
    private static final String SYBASE_BIGTIME = "BIGTIME";

    // ------------------------------blob-------------------------
    private static final String SYBASE_BINARY = "BINARY";
    private static final String SYBASE_VARBINARY = "VARBINARY";
    private static final String SYBASE_IMAGE = "IMAGE";
    private static final String SYBASE_UNITEXT = "UNITEXT";

    // ------------------------------special-------------------------
    private static final String SYBASE_BIT = "BIT";
    private static final String SYBASE_TIMESTAMP = "TIMESTAMP";

    // ------------------------------other-------------------------
    private static final String SYBASE_EXTENDED_TYPE = "EXTENDED_TYPE";

    @Override
    public SeaTunnelDataType<?> mapping(ResultSetMetaData metadata, int colIndex)
            throws SQLException {
        if (metadata.getColumnTypeName(colIndex) == null) {
            return mappingV2ForColumnTypeNameIsNull(metadata, colIndex);
        }
        String sybaseType = metadata.getColumnTypeName(colIndex).toUpperCase();
        int precision = metadata.getPrecision(colIndex);
        int scale = metadata.getScale(colIndex);
        switch (sybaseType) {
            case SYBASE_BIT:
                return BasicType.BOOLEAN_TYPE;
            case SYBASE_TINYINT:
            case SYBASE_SMALLINT:
                return BasicType.SHORT_TYPE;
            case SYBASE_INTEGER:
            case SYBASE_INT:
                return BasicType.INT_TYPE;
            case SYBASE_BIGINT:
                return BasicType.LONG_TYPE;
            case SYBASE_DECIMAL:
            case SYBASE_NUMERIC:
            case SYBASE_MONEY:
            case SYBASE_SMALLMONEY:
                return new DecimalType(precision, scale);
            case SYBASE_FLOAT:
            case SYBASE_DOUBLE_PRECISION:
                return BasicType.DOUBLE_TYPE;
            case SYBASE_REAL:
                return BasicType.FLOAT_TYPE;
            case SYBASE_CHAR:
            case SYBASE_VARCHAR:
            case SYBASE_NCHAR:
            case SYBASE_NVARCHAR:
            case SYBASE_UNICHAR:
            case SYBASE_UNIVARCHAR:
            case SYBASE_SYSNAME:
            case SYBASE_LONGSYSNAME:
            case SYBASE_TEXT:
            case SYBASE_UNITEXT:
            case SYBASE_EXTENDED_TYPE:
                return BasicType.STRING_TYPE;
            case SYBASE_DATE:
                return LocalTimeType.LOCAL_DATE_TYPE;
            case SYBASE_TIME:
                return LocalTimeType.LOCAL_TIME_TYPE;
            case SYBASE_DATETIME:
            case SYBASE_SMALLDATETIME:
            case SYBASE_BIGDATETIME:
            case SYBASE_BIGTIME:
                return LocalTimeType.LOCAL_DATE_TIME_TYPE;
            case SYBASE_BINARY:
            case SYBASE_VARBINARY:
            case SYBASE_IMAGE:
            case SYBASE_TIMESTAMP:
                return PrimitiveByteArrayType.INSTANCE;
            case SYBASE_UNKNOWN:
            default:
                final String jdbcColumnName = metadata.getColumnName(colIndex);
                throw CommonError.convertToSeaTunnelTypeError(
                        DatabaseIdentifier.SYBASE, sybaseType, jdbcColumnName);
        }
    }

    private SeaTunnelDataType<?> mappingV2ForColumnTypeNameIsNull(
            ResultSetMetaData metadata, int colIndex) throws SQLException {
        int columnType = metadata.getColumnType(colIndex);
        int precision = metadata.getPrecision(colIndex);
        int scale = metadata.getScale(colIndex);
        switch (columnType) {
            case Types.TIMESTAMP:
                return LocalTimeType.LOCAL_DATE_TIME_TYPE;
            case Types.NUMERIC:
                return new DecimalType(precision, scale);
            default:
                final String jdbcColumnName = metadata.getColumnName(colIndex);
                throw CommonError.convertToSeaTunnelTypeError(
                        DatabaseIdentifier.SYBASE, String.valueOf(columnType), jdbcColumnName);
        }
    }
}
