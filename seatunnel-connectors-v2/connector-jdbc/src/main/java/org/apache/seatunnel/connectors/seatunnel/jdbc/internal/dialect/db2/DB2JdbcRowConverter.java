/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.db2;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.AbstractJdbcRowConverter;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.DatabaseIdentifier;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class DB2JdbcRowConverter extends AbstractJdbcRowConverter {

    private static final String DB2_CLOB = "CLOB";

    @Override
    public String converterName() {
        return DatabaseIdentifier.DB_2;
    }

    @Override
    public PreparedStatement toExternal(
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            SeaTunnelRow row,
            PreparedStatement statement)
            throws SQLException {
        SeaTunnelRowType rowType = tableSchema.toPhysicalRowDataType();
        final Connection connection = statement.getConnection();
        for (int fieldIndex = 0; fieldIndex < rowType.getTotalFields(); fieldIndex++) {
            SeaTunnelDataType<?> seaTunnelDataType = rowType.getFieldType(fieldIndex);
            final String fieldName = rowType.getFieldName(fieldIndex);
            int statementIndex = fieldIndex + 1;
            Object fieldValue = row.getField(fieldIndex);
            String sourceType = null;
            if (databaseTableSchema != null) {
                sourceType = databaseTableSchema.getColumn(fieldName).getSourceType();
            }
            switch (seaTunnelDataType.getSqlType()) {
                case STRING:
                    if (StringUtils.equalsIgnoreCase(DB2_CLOB, sourceType)) {
                        if (fieldValue == null) {
                            statement.setNull(statementIndex, Types.CLOB);
                        } else {
                            final Clob clob = connection.createClob();
                            clob.setString(1, String.valueOf(row.getField(fieldIndex)));
                            statement.setClob(statementIndex, clob);
                        }
                    } else {
                        if (fieldValue == null) {
                            statement.setNull(statementIndex, Types.VARCHAR);
                        } else {
                            statement.setString(
                                    statementIndex, String.valueOf(row.getField(fieldIndex)));
                        }
                    }
                    break;
                case BOOLEAN:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.BOOLEAN);
                    } else {
                        statement.setBoolean(statementIndex, (Boolean) row.getField(fieldIndex));
                    }
                    break;
                case TINYINT:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.TINYINT);
                    } else {
                        statement.setByte(statementIndex, (Byte) row.getField(fieldIndex));
                    }
                    break;
                case SMALLINT:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.SMALLINT);
                    } else {
                        statement.setShort(statementIndex, (Short) row.getField(fieldIndex));
                    }
                    break;
                case INT:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.INTEGER);
                    } else {
                        statement.setInt(
                                statementIndex,
                                Integer.parseInt(String.valueOf(row.getField(fieldIndex))));
                    }
                    break;
                case BIGINT:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.BIGINT);
                    } else {
                        statement.setLong(statementIndex, (Long) row.getField(fieldIndex));
                    }
                    break;
                case FLOAT:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.FLOAT);
                    } else {
                        statement.setFloat(statementIndex, (Float) row.getField(fieldIndex));
                    }
                    break;
                case DOUBLE:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.DOUBLE);
                    } else {
                        statement.setDouble(statementIndex, (Double) row.getField(fieldIndex));
                    }
                    break;
                case DECIMAL:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.DECIMAL);
                    } else {
                        statement.setBigDecimal(
                                statementIndex, (BigDecimal) row.getField(fieldIndex));
                    }
                    break;
                case DATE:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.DATE);
                    } else {
                        LocalDate localDate = (LocalDate) row.getField(fieldIndex);
                        statement.setDate(statementIndex, java.sql.Date.valueOf(localDate));
                    }
                    break;
                case TIME:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.TIME);
                    } else {
                        LocalTime localTime = (LocalTime) row.getField(fieldIndex);
                        statement.setTime(statementIndex, java.sql.Time.valueOf(localTime));
                    }
                    break;
                case TIMESTAMP:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.TIMESTAMP);
                    } else {
                        LocalDateTime localDateTime = (LocalDateTime) row.getField(fieldIndex);
                        statement.setTimestamp(
                                statementIndex, java.sql.Timestamp.valueOf(localDateTime));
                    }
                    break;
                case BYTES:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.BINARY);
                    } else {
                        statement.setBytes(statementIndex, (byte[]) row.getField(fieldIndex));
                    }
                    break;
                case NULL:
                    statement.setNull(statementIndex, java.sql.Types.NULL);
                    break;
                case MAP:
                case ARRAY:
                case ROW:
                    if (fieldValue == null) {
                        statement.setNull(statementIndex, Types.ARRAY);
                    } else {
                        statement.setString(statementIndex, row.getField(fieldIndex).toString());
                    }
                    break;
                default:
                    throw new JdbcConnectorException(
                            CommonErrorCodeDeprecated.UNSUPPORTED_DATA_TYPE,
                            "Unexpected value: " + seaTunnelDataType);
            }
        }
        return statement;
    }
}
