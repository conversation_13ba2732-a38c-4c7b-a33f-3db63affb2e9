<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-connectors-v2</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>connector-jdbc</artifactId>
    <name>SeaTunnel : Connectors V2 : Jdbc</name>

    <properties>
        <mysql.version>8.0.27</mysql.version>
        <postgresql.version>42.4.3</postgresql.version>
        <dm-jdbc.version>8.1.2.141</dm-jdbc.version>
        <sqlserver.version>9.2.1.jre8</sqlserver.version>
        <phoenix.version>5.2.5-HBase-2.x</phoenix.version>
        <oracle.version>12.2.0.1</oracle.version>
        <sqlite.version>3.39.3.0</sqlite.version>
        <db2.version>db2jcc4</db2.version>
        <sqlite.version>3.39.3.0</sqlite.version>
        <tablestore.version>5.13.9</tablestore.version>
        <teradata.version>17.20.00.12</teradata.version>
        <redshift.version>2.1.0.9</redshift.version>
        <saphana.version>2.14.7</saphana.version>
        <snowflake.version>3.13.29</snowflake.version>
        <vertica.version>12.0.3-0</vertica.version>
        <hikari.version>4.0.3</hikari.version>
        <postgis.jdbc.version>2.5.1</postgis.jdbc.version>
        <kingbase8.version>8.6.0</kingbase8.version>
        <hive.jdbc.version>3.1.3</hive.jdbc.version>
        <oceanbase.jdbc.version>2.4.3</oceanbase.jdbc.version>
        <greenplum.version>5.1.4</greenplum.version>
        <gbase.version>8.3.81.53</gbase.version>
        <impala.version>2.6.12</impala.version>
        <infomix.version>4.50.6</infomix.version>
        <as400.version>9.1</as400.version>
        <sybase.version>6.0</sybase.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ibm.as400</groupId>
                <artifactId>jt400</artifactId>
                <version>${as400.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikari.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.phoenix</groupId>
                <artifactId>ali-phoenix-shaded-thin-client</artifactId>
                <version>${phoenix.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.goldendb</groupId>
                <artifactId>gdb-mysql-connector-java</artifactId>
                <version>5.1.46.28</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>net.postgis</groupId>
                <artifactId>postgis-jdbc</artifactId>
                <version>${postgis.jdbc.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm-jdbc.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${sqlserver.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.xml</groupId>
                <artifactId>xdb6</artifactId>
                <version>${oracle.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.xml</groupId>
                <artifactId>xmlparserv2</artifactId>
                <version>${oracle.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.xerial</groupId>
                <artifactId>sqlite-jdbc</artifactId>
                <version>${sqlite.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.ibm.db2.jcc</groupId>
                <artifactId>db2jcc</artifactId>
                <version>${db2.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>tablestore-jdbc</artifactId>
                <version>${tablestore.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.teradata.jdbc</groupId>
                <artifactId>terajdbc4</artifactId>
                <version>${teradata.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.amazon.redshift</groupId>
                <artifactId>redshift-jdbc42</artifactId>
                <version>${redshift.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.sap.cloud.db.jdbc/ngdbc -->
            <dependency>
                <groupId>com.sap.cloud.db.jdbc</groupId>
                <artifactId>ngdbc</artifactId>
                <version>${saphana.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>net.snowflake</groupId>
                <artifactId>snowflake-jdbc</artifactId>
                <version>${snowflake.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.vertica.jdbc</groupId>
                <artifactId>vertica-jdbc</artifactId>
                <version>${vertica.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>${kingbase8.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.hive</groupId>
                <artifactId>hive-jdbc</artifactId>
                <version>${hive.jdbc.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>jdk.tools</groupId>
                        <artifactId>jdk.tools</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>${oceanbase.jdbc.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pivotal</groupId>
                <artifactId>greenplum-jdbc</artifactId>
                <version>${greenplum.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.esen.jdbc</groupId>
                <artifactId>gbase</artifactId>
                <version>${gbase.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.cloudera</groupId>
                <artifactId>ImpalaJDBC41</artifactId>
                <version>${impala.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.ibm.informix</groupId>
                <artifactId>jdbc</artifactId>
                <version>${infomix.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.sybase</groupId>
                <artifactId>jconn4</artifactId>
                <version>${sybase.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>${hikari.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.as400</groupId>
            <artifactId>jt400</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.goldendb</groupId>
            <artifactId>gdb-mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>net.postgis</groupId>
            <artifactId>postgis-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.phoenix</groupId>
            <artifactId>ali-phoenix-shaded-thin-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.xml</groupId>
            <artifactId>xdb6</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.xml</groupId>
            <artifactId>xmlparserv2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibm.db2.jcc</groupId>
            <artifactId>db2jcc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>tablestore-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teradata.jdbc</groupId>
            <artifactId>terajdbc4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazon.redshift</groupId>
            <artifactId>redshift-jdbc42</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sap.cloud.db.jdbc</groupId>
            <artifactId>ngdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>net.snowflake</groupId>
            <artifactId>snowflake-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vertica.jdbc</groupId>
            <artifactId>vertica-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pivotal</groupId>
            <artifactId>greenplum-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.esen.jdbc</groupId>
            <artifactId>gbase</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cloudera</groupId>
            <artifactId>ImpalaJDBC41</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.informix</groupId>
            <artifactId>jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sybase</groupId>
            <artifactId>jconn4</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <createSourcesJar>false</createSourcesJar>
                            <shadeSourcesContent>true</shadeSourcesContent>
                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <relocations>
                                <!-- rename hikari to avoid jar conflict from spark -->
                                <relocation>
                                    <pattern>com.zaxxer.hikari</pattern>
                                    <shadedPattern>${seatunnel.shade.package}.com.zaxxer.hikari</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
