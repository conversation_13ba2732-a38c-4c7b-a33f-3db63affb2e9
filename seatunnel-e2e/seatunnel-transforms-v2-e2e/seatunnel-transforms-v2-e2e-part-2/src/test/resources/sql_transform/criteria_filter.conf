#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  FakeSource {
    result_table_name = "fake"
    schema = {
      fields {
        id = "int"
        name = "string"
        age = "int"
        email = "string"
      }
    }
    rows = [
      {fields = [1, "Joy Ding", 20, null], kind = INSERT}
      {fields = [2, "May Ding", 22, "<EMAIL>"], kind = INSERT}
      {fields = [3, "Kin Dom", 21, "<EMAIL>"], kind = INSERT}
      {fields = [4, "LeBron Ding", 38, null], kind = INSERT}
      {fields = [8, "Wang DingCC", 34, null], kind = INSERT}
      {fields = [9, "Zu DingDD", 33, null], kind = INSERT}
      {fields = [10, "Zhang DingEE", 40, null], kind = INSERT}
      {fields = [11, "Lin Qiang", 40, null], kind = INSERT}
      {fields = [12, "Yu Liang", 40, null], kind = INSERT}
    ]
  }
}

transform {
  Sql {
    source_table_name = "fake"
    result_table_name = "fake1"
    query = """
      select id, name, age, email from fake
              where ( id = 1 or id = 4 or id in (8, 9, 10, 11, 12) )
                  and id != 0 and name <> 'Kin Dom'
                  and ( age >= 20 or age < 22 )
                  and regexp_like(name, '[A-Z ]*')
                  and id > 0 and id >= 1 and id in (1, 2, 3, 4, 8, 9, 10, 11, 12)
                  and id not in (5, 6, 7) and name is not null and email is null
                  and id < 500 and id <= 500
                  and ( name like '%Din_' or name like 'Wan_%' or name like '%Yu%' )
                  and name not like '%LeBron%'
                  and name not like 'Wan_%'
                  and name not like '%Lian_'
    """
  }
}
sink {
  Assert {
    source_table_name = "fake1"
    rules =
      {
        row_rules = [
          {
            rule_type = MIN_ROW
            rule_value = 1
          },
          {
            rule_type = MAX_ROW
            rule_value = 1
          }
        ],
        field_rules = [
          {
            field_name = "id"
            field_type = "int"
            field_value = [
              {equals_to = 1}
            ]
          }
        ]
      }
  }
}

