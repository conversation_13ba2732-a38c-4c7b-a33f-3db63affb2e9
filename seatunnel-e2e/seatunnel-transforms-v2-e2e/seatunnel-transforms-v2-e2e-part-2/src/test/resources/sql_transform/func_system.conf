#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  FakeSource {
    result_table_name = "fake"
    schema = {
      fields {
        id = "bigint"
        name = "string"
        c1 = "string"
        c2 = "timestamp"
        c3 = "string"
        c4 = "bigint"
        c5 = "int"
        c6 = "int"
      }
    }
    rows = [
      {fields = [1, "Joy Ding", "12.4", "2012-12-21T12:34:56", null, 1687747869032, 20230625, 235109], kind = INSERT}
    ]
  }
}

transform {
  Sql {
    source_table_name = "fake"
    result_table_name = "fake1"
    query = "select cast(id as STRING) as id, cast(id as INT) as id2, cast(id as DOUBLE) as id3 , cast(c1 as double) as c1_1, cast(c1 as DECIMAL(10,2)) as c1_2, cast(c2 as DATE) as c2_1, coalesce(c3,'Unknown') c3_1, ifnull(c3,'Unknown') c3_2, ifnull(nullif(name,'Joy Ding'),'NULL') name1, nullif(name,'Joy Ding_') name2, cast(c4 as timestamp) as c4_1, cast(c4 as decimal(17,4)) as c4_2, cast(c5 as date) as c5, cast(c6 as time) as c6 from fake"
  }
}

sink {
  Assert {
    source_table_name = "fake1"
    rules = {
      field_rules = [
        {
          field_name = "id"
          field_type = "string"
          field_value = [
            {equals_to = "1"}
          ]
        },
        {
          field_name = "id2"
          field_type = "int"
          field_value = [
            {equals_to = 1}
          ]
        },
        {
          field_name = "id3"
          field_type = "double"
          field_value = [
            {equals_to = 1}
          ]
        },
        {
          field_name = "c1_1"
          field_type = "double"
          field_value = [
            {equals_to = 12.4}
          ]
        },
        {
          field_name = "c1_2"
          field_type = "decimal(10,2)"
          field_value = [
            {equals_to = "12.40"}
          ]
        },
        {
          field_name = "c2_1"
          field_type = "date"
          field_value = [
            {equals_to = "2012-12-21"}
          ]
        },
        {
          field_name = "c3_1"
          field_type = "string"
          field_value = [
            {equals_to = "Unknown"}
          ]
        },
        {
          field_name = "c3_2"
          field_type = "string"
          field_value = [
            {equals_to = "Unknown"}
          ]
        },
        {
          field_name = "name1"
          field_type = "string"
          field_value = [
            {equals_to = "NULL"}
          ]
        },
        {
          field_name = "name2"
          field_type = "string"
          field_value = [
            {equals_to = "Joy Ding"}
          ]
        },
        {
          field_name = "c4_1"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2023-06-26T02:51:09.032"}
          ]
        },
        {
          field_name = "c4_2"
          field_type = "decimal(17,4)"
          field_value = [
            {equals_to = "1687747869032.0000"}
          ]
        },
        {
          field_name = "c5"
          field_type = "date"
          field_value = [
            {equals_to = "2023-06-25"}
          ]
        },
        {
          field_name = "c6"
          field_type = "time"
          field_value = [
            {equals_to = "23:51:09"}
          ]
        }
      ]
    }
  }
}