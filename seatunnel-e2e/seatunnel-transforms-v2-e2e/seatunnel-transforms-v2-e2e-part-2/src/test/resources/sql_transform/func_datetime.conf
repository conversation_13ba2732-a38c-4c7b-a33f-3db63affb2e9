#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  FakeSource {
    result_table_name = "fake"
    schema = {
      fields {
        id = "int"
        name = "string"
        c1 = "timestamp"
        c2 = "timestamp"
        c3 = "timestamp"
        c4 = "timestamp"
        c5 = "string"
        c6 = "string"
      }
    }
    rows = [
      {fields = [1, "Joy Ding", "2021-04-15T13:34:45", "2022-01-23T12:34:56", "2021-04-15T13:34:45.235", "2021-04-08T13:34:45.235", "2021-04-08 13:34:45.235", "2021-04-08"], kind = INSERT}
    ]
  }
}

transform {
  Sql {
    source_table_name = "fake"
    result_table_name = "fake1"
    query = "select current_date as cd, current_timestamp as ct2, dateadd(c1, 1) as c1_1, dateadd(c1, 40, 'DAY') as c1_2, dateadd(c1, 2, 'YEAR') as c1_3, dateadd(c1, 10, 'MONTH') as c1_4, dateadd(c1, 13, 'HOUR') as c1_5, dateadd(c1, 40, 'MINUTE') as c1_6, dateadd(c1, 30, 'SECOND') as c1_7, datediff(c1, c2) as test, datediff(c1, c2, 'DAY') as c2_1, datediff(c1, c2, 'YEAR') as c2_2, datediff(c1, c2, 'MONTH') as c2_3, datediff(c1, c2, 'HOUR') as c2_4, datediff(c1, c2, 'MINUTE') as c2_5, datediff(c1, c2, 'SECOND') as c2_6, date_trunc(c3, 'YEAR') as c3_1, date_trunc(c3, 'MONTH') as c3_2, date_trunc(c3, 'DAY') as c3_3, date_trunc(c3, 'HOUR') as c3_4, date_trunc(c3, 'MINUTE') as c3_5, date_trunc(c3, 'SECOND') as c3_6, dayname(c3) as c3_7, day_of_week(c3) c3_8, day_of_year(c3) c3_9, extract(YEAR FROM c3) c3_10, extract(MONTH FROM c3) c3_11, extract(DAY FROM c3) c3_12, extract(HOUR FROM c3) c3_13, extract(MINUTE from c3) c3_14, extract(SECOND from c3) c3_15, extract(MILLISECOND from c3) c3_16, extract(DAYOFWEEK FROM c3) c3_17, extract(DAYOFYEAR FROM c3) c3_18, formatdatetime(c4,'yyyy-MM-dd HH:mm:ss.S') c4_1, formatdatetime(c4,'yyyy-MM-dd') c4_2, formatdatetime(c4,'HH:mm:ss.SSS') c4_3, hour(c4) c4_4, minute(c4) c4_5, month(c4) c4_6, monthname(c4) c4_7, parsedatetime(c5,'yyyy-MM-dd HH:mm:ss.SSS') c5_1, to_date(c6,'yyyy-MM-dd') c6_1, quarter(c4) c4_8, second(c4) c4_9, week(c4) c4_10, year(c4) c4_11 from fake"
  }
}

sink {
  Assert {
    source_table_name = "fake1"
    rules = {
      field_rules = [
        {
          field_name = "cd"
          field_type = "date"
          field_value = [
            {rule_type = NOT_NULL}
          ]
        },
        {
          field_name = "ct2"
          field_type = "timestamp"
          field_value = [
            {rule_type = NOT_NULL}
          ]
        },
        {
          field_name = "c1_1"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-16T13:34:45"}
          ]
        },
        {
          field_name = "c1_2"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-05-25T13:34:45"}
          ]
        },
        {
          field_name = "c1_3"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2023-04-15T13:34:45"}
          ]
        },
        {
          field_name = "c1_4"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2022-02-15T13:34:45"}
          ]
        },
        {
          field_name = "c1_5"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-16T02:34:45"}
          ]
        },
        {
          field_name = "c1_6"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-15T14:14:45"}
          ]
        },
        {
          field_name = "c1_7"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-15T13:35:15"}
          ]
        },
        {
          field_name = "test"
          field_type = bigint
          field_value = [
            {equals_to = 283}
          ]
        },
        {
          field_name = "c2_1"
          field_type = bigint
          field_value = [
            {equals_to = 283}
          ]
        },
        {
          field_name = "c2_2"
          field_type = bigint
          field_value = [
            {equals_to = 0}
          ]
        },
        {
          field_name = "c2_3"
          field_type = bigint
          field_value = [
            {equals_to = 9}
          ]
        },
        {
          field_name = "c2_4"
          field_type = bigint
          field_value = [
            {equals_to = 6791}
          ]
        },
        {
          field_name = "c2_5"
          field_type = bigint
          field_value = [
            {equals_to = 407460}
          ]
        },
        {
          field_name = "c2_6"
          field_type = bigint
          field_value = [
            {equals_to = 24447611}
          ]
        },
        {
          field_name = "c3_1"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-01-01T00:00:00"}
          ]
        },
        {
          field_name = "c3_2"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-01T00:00:00"}
          ]
        },
        {
          field_name = "c3_3"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-15T00:00:00"}
          ]
        },
        {
          field_name = "c3_4"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-15T13:00:00"}
          ]
        },
        {
          field_name = "c3_5"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-15T13:34:00"}
          ]
        },
        {
          field_name = "c3_6"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-15T13:34:45"}
          ]
        },
        {
          field_name = "c3_7"
          field_type = "string"
          field_value = [
            {equals_to = "Thursday"}
          ]
        },
        {
          field_name = "c3_8"
          field_type = "int"
          field_value = [
            # Thursday
            {equals_to = 4}
          ]
        },
        {
          field_name = "c3_9"
          field_type = "int"
          field_value = [
            {equals_to = 105}
          ]
        },
        {
          field_name = "c3_10"
          field_type = "int"
          field_value = [
            {equals_to = 2021}
          ]
        },
        {
          field_name = "c3_11"
          field_type = "int"
          field_value = [
            {equals_to = 4}
          ]
        },
        {
          field_name = "c3_12"
          field_type = "int"
          field_value = [
            {equals_to = 15}
          ]
        },
        {
          field_name = "c3_13"
          field_type = "int"
          field_value = [
            {equals_to = 13}
          ]
        },
        {
          field_name = "c3_14"
          field_type = "int"
          field_value = [
            {equals_to = 34}
          ]
        },
        {
          field_name = "c3_15"
          field_type = "int"
          field_value = [
            {equals_to = 45}
          ]
        },
        {
          field_name = "c3_16"
          field_type = "int"
          field_value = [
            {equals_to = 235}
          ]
        },
        {
          field_name = "c3_17"
          field_type = "int"
          field_value = [
            {equals_to = 4}
          ]
        },
        {
          field_name = "c3_18"
          field_type = "int"
          field_value = [
            {equals_to = 105}
          ]
        },
        {
          field_name = "c4_1"
          field_type = "string"
          field_value = [
            {equals_to = "2021-04-08 13:34:45.2"}
          ]
        },
        {
          field_name = "c4_2"
          field_type = "string"
          field_value = [
            {equals_to = "2021-04-08"}
          ]
        },
        {
          field_name = "c4_3"
          field_type = "string"
          field_value = [
            {equals_to = "13:34:45.235"}
          ]
        },
        {
          field_name = "c4_4"
          field_type = "int"
          field_value = [
            {equals_to = 13}
          ]
        },
        {
          field_name = "c4_5"
          field_type = "int"
          field_value = [
            {equals_to = 34}
          ]
        },
        {
          field_name = "c4_6"
          field_type = "int"
          field_value = [
            {equals_to = 4}
          ]
        },
        {
          field_name = "c4_7"
          field_type = "string"
          field_value = [
            {equals_to = "April"}
          ]
        },
        {
          field_name = "c5_1"
          field_type = "timestamp"
          field_value = [
            {equals_to = "2021-04-08T13:34:45.235"}
          ]
        },
        {
          field_name = "c6_1"
          field_type = "date"
          field_value = [
            {equals_to = "2021-04-08"}
          ]
        },
        {
          field_name = "c4_8"
          field_type = "int"
          field_value = [
            {equals_to = 2}
          ]
        },
        {
          field_name = "c4_9"
          field_type = "int"
          field_value = [
            {equals_to = 45}
          ]
        },
        {
          field_name = "c4_10"
          field_type = "int"
          field_value = [
            {equals_to = 15}
          ]
        },
        {
          field_name = "c4_11"
          field_type = "int"
          field_value = [
            {equals_to = 2021}
          ]
        }
      ]
    }
  }
}