#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  FakeSource {
    result_table_name = "fake"
    schema = {
      fields {
        id = "int"
        name = "string"
        price = "double"
      }
    }
    rows = [
      {fields = [1, "Joy Ding", 134.22], kind = INSERT}
    ]
  }
}

transform {
  Sql {
    source_table_name = "fake"
    result_table_name = "fake1"
    query = "select id+1 as id, id*4 as id2, price/3 as price, price-34.22 as price2, price%23.12 as price3, name||'_'||id as name from fake"
  }
}

sink {
  Assert {
    source_table_name = "fake1"
    rules = {
      field_rules = [
        {
          field_name = "id"
          field_type = "int"
          field_value = [
            {equals_to = 2}
          ]
        },
        {
          field_name = "id2"
          field_type = "int"
          field_value = [
            {equals_to = 4}
          ]
        },
        {
          field_name = "price"
          field_type = "double"
          field_value = [
            {equals_to = 44.74}
          ]
        },
        {
          field_name = "price2"
          field_type = "double"
          field_value = [
            {equals_to = 100}
          ]
        },
        {
          field_name = "price3"
          field_type = "double"
          field_value = [
            {equals_to = 18.619999999999994}
          ]
        },
        {
          field_name = "name"
          field_type = "string"
          field_value = [
            {equals_to = "Joy Ding_1"}
          ]
        }
      ]
    }
  }
}