#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  FakeSource {
    result_table_name = "fake"
    schema = {
      fields {
        id = "int"
        name = "string"
        c1 = "double"
        c2 = "double"
        c3 = "double"
        c4 = "int"
        c5 = "double"
        c6 = "double"
        c7 = "int"
        c8 = "double"
        c9 = "double"
        c10 = "double"
      }
    }
    rows = [
      {fields = [1, "Joy Ding", -120.72, 0, 3.1415926, 13, 13.2, 1324.252, 180, 10.24, 120.72124, 2], kind = INSERT}
    ]
  }
}

transform {
  Sql {
    source_table_name = "fake"
    result_table_name = "fake1"
    query = "select abs(c1) as c1_1, acos(id) as id1, asin(c2) as c2_1, atan(c2) as c2_2, cos(c2) as c2_3, cosh(c2) as c2_4, sin(c2) as c2_5, sinh(c2) as c2_6, tan(c3/4) as c3_1, tanh(c2) as c2_7, mod(c4, 5) as c4_1, mod(c4, 5.4) as c4_2, ceil(c5) as c5_1, exp(c10) as c10_1, floor(c5) as c5_2, ln(c5) as c5_3, log(10,c5) as c5_4, log10(c6) as c6_1, radians(c7) as c7_1, sqrt(c8) as c8_1, pi() as pi, power(c5,2) as c5_5, rand() as rand, round(c9,2) as c9_1, sign(c1) as c1_2, trunc(c9,2) as c9_2 from fake"
  }
}

sink {
  Assert {
    source_table_name = "fake1"
    rules = {
      field_rules = [
        {
          field_name = "c1_1"
          field_type = "double"
          field_value = [
            {equals_to = 120.72}
          ]
        },
        {
          field_name = "id1"
          field_type = "double"
          field_value = [
            {equals_to = 0}
          ]
        },
        {
          field_name = "c2_1"
          field_type = "double"
          field_value = [
            {equals_to = 0}
          ]
        },
        {
          field_name = "c2_2"
          field_type = "double"
          field_value = [
            {equals_to = 0.0}
          ]
        },
        {
          field_name = "c2_3"
          field_type = "double"
          field_value = [
            {equals_to = 1}
          ]
        },
        {
          field_name = "c2_4"
          field_type = "double"
          field_value = [
            {equals_to = 1}
          ]
        },
        {
          field_name = "c2_5"
          field_type = "double"
          field_value = [
            {equals_to = 0.0}
          ]
        },
        {
          field_name = "c2_6"
          field_type = "double"
          field_value = [
            {equals_to = 0.0}
          ]
        },
        {
          field_name = "c3_1"
          field_type = "double"
          field_value = [
            {equals_to = 0.9999999732051038}
          ]
        },
        {
          field_name = "c2_7"
          field_type = "double"
          field_value = [
            {equals_to = 0.0}
          ]
        },
        {
          field_name = "c4_1"
          field_type = "int"
          field_value = [
            {equals_to = 3}
          ]
        },
        {
          field_name = "c4_2"
          field_type = "double"
          field_value = [
            {equals_to = 2.2}
          ]
        },
        {
          field_name = "c5_1"
          field_type = "int"
          field_value = [
            {equals_to = 14}
          ]
        },
        {
          field_name = "c10_1"
          field_type = "double"
          field_value = [
            {equals_to = 7.38905609893065}
          ]
        },
        {
          field_name = "c5_2"
          field_type = "int"
          field_value = [
            {equals_to = 13}
          ]
        },
        {
          field_name = "c5_3"
          field_type = "double"
          field_value = [
            {equals_to = 2.580216829592325}
          ]
        },
        {
          field_name = "c5_4"
          field_type = "double"
          field_value = [
            {equals_to = 1.1205739312058498}
          ]
        },
        {
          field_name = "c6_1"
          field_type = "double"
          field_value = [
            {equals_to = 3.1219706375172507}
          ]
        },
        {
          field_name = "c7_1"
          field_type = "double"
          field_value = [
            {equals_to = 3.141592653589793}
          ]
        },
        {
          field_name = "c8_1"
          field_type = "double"
          field_value = [
            {equals_to = 3.2}
          ]
        },
        {
          field_name = "pi"
          field_type = "double"
          field_value = [
            {equals_to = 3.141592653589793}
          ]
        },
        {
          field_name = "c5_5"
          field_type = "double"
          field_value = [
            {equals_to = 174.23999999999998}
          ]
        },
        {
          field_name = "c9_1"
          field_type = "double"
          field_value = [
            {equals_to = 120.72}
          ]
        },
        {
          field_name = "c1_2"
          field_type = "int"
          field_value = [
            {equals_to = -1}
          ]
        },
        {
          field_name = "c9_2"
          field_type = "double"
          field_value = [
            {equals_to = 120.72}
          ]
        }
      ]
    }
  }
}