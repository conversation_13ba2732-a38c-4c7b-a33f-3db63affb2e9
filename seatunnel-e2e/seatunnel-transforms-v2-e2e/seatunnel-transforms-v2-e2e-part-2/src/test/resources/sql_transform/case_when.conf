#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######


env {
  parallelism = 1
  job.mode = "BATCH"
}


source {
  FakeSource {
    result_table_name = "fake"
    schema = {
      fields {
        c_string = string
        c_boolean = boolean
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
        c_decimal = "decimal(30, 8)"
        c_bytes = bytes
        c_date = date
        c_timestamp = timestamp
      }
    }
    rows = [
      {
        kind = INSERT
        fields = ["c_string", true, 117, 15987, 56387395, 7084913402530365000, 1.23, 1.23, "2924137191386439303744.39292216", "bWlJWmo=", "2023-04-22", "2023-04-22T23:20:58"]
      }
    ]
  }
}

transform {
  Sql {
    source_table_name = "fake"
    result_table_name = "fake1"
    query = """
      select case when c_string in ('c_string') then 1 else 0 end     as c_string_1,
       case when c_string not in ('c_string') then 1 else 0 end as c_string_0,
       case when c_tinyint = 117 and TO_CHAR(c_boolean)='true' then 1 else 0 end as c_tinyint_boolean_1,
       case when c_tinyint != 117 and TO_CHAR(c_boolean)='true' then 1 else 0 end as c_tinyint_boolean_0,
       case when c_tinyint != 117 or TO_CHAR(c_boolean)='true' then 1 else 0 end as c_tinyint_boolean_or_1,
       case when c_int > 1 and c_bigint >1 and c_float >1 and c_double > 1 and c_decimal > 1 then 1 else 0 end as c_number_1,
       case when c_tinyint <> 117 then 1 else 0 end as c_number_0
       from fake
    """
  }
}


sink {
  Assert {
    source_table_name = "fake1"
    rules =
      {
        row_rules = [
          {
            rule_type = MIN_ROW
            rule_value = 1
          },
          {
            rule_type = MAX_ROW
            rule_value = 1
          }
        ],
        field_rules = [
          {
            field_name = "c_string_1"
            field_type = "int"
            field_value = [
              {equals_to = 1}
            ]
          }, {
            field_name = "c_string_0"
            field_type = "int"
            field_value = [
              {equals_to = 0}
            ]
          }, {
            field_name = "c_tinyint_boolean_1"
            field_type = "int"
            field_value = [
              {equals_to = 1}
            ]
          }, {
            field_name = "c_tinyint_boolean_0"
            field_type = "int"
            field_value = [
              {equals_to = 0}
            ]
          }, {
            field_name = "c_tinyint_boolean_or_1"
            field_type = "int"
            field_value = [
              {equals_to = 1}
            ]
          }, {
            field_name = "c_number_1"
            field_type = "int"
            field_value = [
              {equals_to = 1}
            ]
          }, {
            field_name = "c_number_0"
            field_type = "int"
            field_value = [
              {equals_to = 0}
            ]
          }
        ]
      }
  }
}