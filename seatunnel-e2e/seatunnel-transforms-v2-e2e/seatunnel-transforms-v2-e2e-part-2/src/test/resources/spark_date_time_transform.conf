#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  job.mode = "BATCH"
}

source {
  FakeSource {
    result_table_name = "fake"
    row.num = 100
    schema = {
      fields {
        id = "int"
        name = "string"
        c_time = "timestamp"
        c_date = "date"
      }
    }
  }
}

transform {
  Replace {
    source_table_name = "fake"
    result_table_name = "fake1"
    replace_field = "name"
    pattern = ".+"
    replacement = "b"
    is_regex = true
    replace_first = true
  }
}

sink {
  Assert {
    source_table_name = "fake1"
    rules =
      {
        row_rules = [
          {
            rule_type = MIN_ROW
            rule_value = 100
          }
        ],
        field_rules = [
          {
            field_name = id
            field_type = int
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = name
            field_type = string
            field_value = [
              {
                rule_type = NOT_NULL
              },
              {
                rule_type = MIN_LENGTH
                rule_value = 1
              },
              {
                rule_type = MAX_LENGTH
                rule_value = 1
              }
            ]
          },
          {
            field_name = c_time
            field_type = timestamp
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_date
            field_type = date
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          }
        ]
      }
  }
}