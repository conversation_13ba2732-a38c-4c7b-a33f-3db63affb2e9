#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  job.mode = "BATCH"
}

source {
  FakeSource {
    result_table_name = "fake"
    row.num = 100
    string.fake.mode = "template"
    string.template=["{"data":{"c_string": "this is a string","c_boolean": "true","c_integer": "42","c_float": "3.14","c_double": "3.14","c_decimal": "10.55","c_date":"'2023-10-29'","c_datetime":\"16:12:43.459\"}}"]
    schema = {
      fields {
        data = "string"
      }
    }
  }
}

transform {
  JsonPath {
    source_table_name = "fake"
    result_table_name = "fake1"
    columns = [
     {
        "src_field" = "data"
        "path" = "$.data.c_string"
        "dest_field" = "c1_string"
     },
     {
        "src_field" = "data"
        "path" = "$.data.c_boolean"
        "dest_field" = "c1_boolean"
        "dest_type" = "boolean"
     },
     {
        "src_field" = "data"
        "path" = "$.data.c_integer"
        "dest_field" = "c1_integer"
        "dest_type" = "int"
     },
     {
        "src_field" = "data"
        "path" = "$.data.c_float"
        "dest_field" = "c1_float"
        "dest_type" = "float"
     },
     {
        "src_field" = "data"
        "path" = "$.data.c_double"
        "dest_field" = "c1_double"
        "dest_type" = "double"
     },
      {
         "src_field" = "data"
         "path" = "$.data.c_decimal"
         "dest_field" = "c1_decimal"
         "dest_type" = "decimal(4,2)"
      },
      {
         "src_field" = "data"
         "path" = "$.data.c_date"
         "dest_field" = "c1_date"
         "dest_type" = "date"
      },
      {
         "src_field" = "data"
         "path" = "$.data.c_datetime"
         "dest_field" = "c1_datetime"
         "dest_type" = "time"
      }
    ]
  }
}

sink {
  Assert {
    source_table_name = "fake1"
    rules =
      {
        row_rules = [
          {
            rule_type = MIN_ROW
            rule_value = 100
          }
        ],
        field_rules = [
          {
            field_name = c1_string
            field_type = string
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = "this is a string"
              }
            ]
          },
          {
            field_name = c1_boolean
            field_type = boolean
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = "true"
              }
            ]
          },
          {
            field_name = c1_integer
            field_type = int
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = 42
              }
            ]
          },
          {
            field_name = c1_float
            field_type = float
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = 3.14
              }
            ]
          },
          {
            field_name = c1_double
            field_type = double
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = 3.14
              }
            ]
          },
          {
            field_name = c1_decimal
            field_type = "decimal(4,2)"
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = 10.55
              }
            ]
          },
          {
            field_name = c1_date
            field_type = date
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = "2023-10-29"
              }
            ]
          },
          {
            field_name = c1_datetime
            field_type = time
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = "16:12:43.459"
              }
            ]
          }
        ]
      }
  }
}