#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  job.mode = "BATCH"
}

source {
  FakeSource {
    result_table_name = "fake"
    row.num = 100
    schema = {
      fields {
        id = "int"
        name = "string"
        age = "int"
        string1 = "string"
        int1 = "int"
        c_bigint = "bigint"
        c_row = {
          c_row = {
            c_int = int
          }
        }
      }
    }
  }
}

transform {
  FieldMapper {
    source_table_name = "fake"
    result_table_name = "fake1"
    field_mapper = {
      id = id
      age = age_as
      int1 = int1_as
      name = name
      c_row = c_row
    }
  }
}

sink {
  Assert {
    source_table_name = "fake1"
    rules =
      {
        row_rules = [
          {
            rule_type = MIN_ROW
            rule_value = 100
          }
        ],
        field_rules = [
          {
            field_name = id
            field_type = int
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = age_as
            field_type = int
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = int1_as
            field_type = int
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = name
            field_type = string
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          }
        ]
      }
  }
}