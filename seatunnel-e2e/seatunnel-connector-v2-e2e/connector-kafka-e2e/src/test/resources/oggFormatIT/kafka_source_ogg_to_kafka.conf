#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"

  #spark config
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 1
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  Kafka {
    bootstrap.servers = "kafka_e2e:9092"
    topic = "test-ogg-source"
    consumer.group = "ogg_format_to_kafka"
    start_mode = earliest
    schema = {
      fields {
        id = "int"
        name = "string"
        description = "string"
        weight = "string"
      }
    },
    format = ogg_json
  }
}

sink {
  Kafka {
    bootstrap.servers = "kafka_e2e:9092"
    topic = "test-ogg-sink"
    format = ogg_json
    partition = 0
  }
}