{"schema":{"type":"struct","fields":[{"type":"struct","fields":[{"type":"int32","optional":false,"field":"id"},{"type":"bytes","optional":true,"field":"f_binary"},{"type":"bytes","optional":true,"field":"f_blob"},{"type":"bytes","optional":true,"field":"f_long_varbinary"},{"type":"bytes","optional":true,"field":"f_longblob"},{"type":"bytes","optional":true,"field":"f_tinyblob"},{"type":"bytes","optional":true,"field":"f_varbinary"},{"type":"int16","optional":true,"field":"f_smallint"},{"type":"int32","optional":true,"field":"f_smallint_unsigned"},{"type":"int32","optional":true,"field":"f_mediumint"},{"type":"int32","optional":true,"field":"f_mediumint_unsigned"},{"type":"int32","optional":true,"field":"f_int"},{"type":"int64","optional":true,"field":"f_int_unsigned"},{"type":"int32","optional":true,"field":"f_integer"},{"type":"int64","optional":true,"field":"f_integer_unsigned"},{"type":"int64","optional":true,"field":"f_bigint"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0"},"field":"f_bigint_unsigned"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_numeric"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_decimal"},{"type":"double","optional":true,"field":"f_float"},{"type":"double","optional":true,"field":"f_double"},{"type":"double","optional":true,"field":"f_double_precision"},{"type":"string","optional":true,"field":"f_longtext"},{"type":"string","optional":true,"field":"f_mediumtext"},{"type":"string","optional":true,"field":"f_text"},{"type":"string","optional":true,"field":"f_tinytext"},{"type":"string","optional":true,"field":"f_varchar"},{"type":"int32","optional":true,"name":"io.debezium.time.Date","version":1,"field":"f_date"},{"type":"int64","optional":true,"name":"io.debezium.time.Timestamp","version":1,"field":"f_datetime"},{"type":"string","optional":true,"name":"io.debezium.time.ZonedTimestamp","version":1,"field":"f_timestamp"},{"type":"boolean","optional":true,"field":"f_bit1"},{"type":"bytes","optional":true,"name":"io.debezium.data.Bits","version":1,"parameters":{"length":"64"},"field":"f_bit64"},{"type":"string","optional":true,"field":"f_char"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"enum1,enum2,enum3"},"field":"f_enum"},{"type":"bytes","optional":true,"field":"f_mediumblob"},{"type":"string","optional":true,"field":"f_long_varchar"},{"type":"double","optional":true,"field":"f_real"},{"type":"int64","optional":true,"name":"io.debezium.time.MicroTime","version":1,"field":"f_time"},{"type":"int16","optional":true,"field":"f_tinyint"},{"type":"int16","optional":true,"field":"f_tinyint_unsigned"},{"type":"string","optional":true,"name":"io.debezium.data.Json","version":1,"field":"f_json"},{"type":"int32","optional":true,"name":"io.debezium.time.Year","version":1,"field":"f_year"}],"optional":true,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Value","field":"before"},{"type":"struct","fields":[{"type":"int32","optional":false,"field":"id"},{"type":"bytes","optional":true,"field":"f_binary"},{"type":"bytes","optional":true,"field":"f_blob"},{"type":"bytes","optional":true,"field":"f_long_varbinary"},{"type":"bytes","optional":true,"field":"f_longblob"},{"type":"bytes","optional":true,"field":"f_tinyblob"},{"type":"bytes","optional":true,"field":"f_varbinary"},{"type":"int16","optional":true,"field":"f_smallint"},{"type":"int32","optional":true,"field":"f_smallint_unsigned"},{"type":"int32","optional":true,"field":"f_mediumint"},{"type":"int32","optional":true,"field":"f_mediumint_unsigned"},{"type":"int32","optional":true,"field":"f_int"},{"type":"int64","optional":true,"field":"f_int_unsigned"},{"type":"int32","optional":true,"field":"f_integer"},{"type":"int64","optional":true,"field":"f_integer_unsigned"},{"type":"int64","optional":true,"field":"f_bigint"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0"},"field":"f_bigint_unsigned"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_numeric"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_decimal"},{"type":"double","optional":true,"field":"f_float"},{"type":"double","optional":true,"field":"f_double"},{"type":"double","optional":true,"field":"f_double_precision"},{"type":"string","optional":true,"field":"f_longtext"},{"type":"string","optional":true,"field":"f_mediumtext"},{"type":"string","optional":true,"field":"f_text"},{"type":"string","optional":true,"field":"f_tinytext"},{"type":"string","optional":true,"field":"f_varchar"},{"type":"int32","optional":true,"name":"io.debezium.time.Date","version":1,"field":"f_date"},{"type":"int64","optional":true,"name":"io.debezium.time.Timestamp","version":1,"field":"f_datetime"},{"type":"string","optional":true,"name":"io.debezium.time.ZonedTimestamp","version":1,"field":"f_timestamp"},{"type":"boolean","optional":true,"field":"f_bit1"},{"type":"bytes","optional":true,"name":"io.debezium.data.Bits","version":1,"parameters":{"length":"64"},"field":"f_bit64"},{"type":"string","optional":true,"field":"f_char"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"enum1,enum2,enum3"},"field":"f_enum"},{"type":"bytes","optional":true,"field":"f_mediumblob"},{"type":"string","optional":true,"field":"f_long_varchar"},{"type":"double","optional":true,"field":"f_real"},{"type":"int64","optional":true,"name":"io.debezium.time.MicroTime","version":1,"field":"f_time"},{"type":"int16","optional":true,"field":"f_tinyint"},{"type":"int16","optional":true,"field":"f_tinyint_unsigned"},{"type":"string","optional":true,"name":"io.debezium.data.Json","version":1,"field":"f_json"},{"type":"int32","optional":true,"name":"io.debezium.time.Year","version":1,"field":"f_year"}],"optional":true,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Value","field":"after"},{"type":"struct","fields":[{"type":"string","optional":false,"field":"version"},{"type":"string","optional":false,"field":"connector"},{"type":"string","optional":false,"field":"name"},{"type":"int64","optional":false,"field":"ts_ms"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"true,last,false"},"default":"false","field":"snapshot"},{"type":"string","optional":false,"field":"db"},{"type":"string","optional":true,"field":"sequence"},{"type":"string","optional":true,"field":"table"},{"type":"int64","optional":false,"field":"server_id"},{"type":"string","optional":true,"field":"gtid"},{"type":"string","optional":false,"field":"file"},{"type":"int64","optional":false,"field":"pos"},{"type":"int32","optional":false,"field":"row"},{"type":"int64","optional":true,"field":"thread"},{"type":"string","optional":true,"field":"query"}],"optional":false,"name":"io.debezium.connector.mysql.Source","field":"source"},{"type":"string","optional":false,"field":"op"},{"type":"int64","optional":true,"field":"ts_ms"},{"type":"struct","fields":[{"type":"string","optional":false,"field":"id"},{"type":"int64","optional":false,"field":"total_order"},{"type":"int64","optional":false,"field":"data_collection_order"}],"optional":true,"field":"transaction"}],"optional":false,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Envelope"},"payload":{"before":null,"after":{"id":1,"f_binary":"YWJjdAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==","f_blob":"aGVsbG8=","f_long_varbinary":"GAAAAHicC8nILFYAokSFnPy8dIWyxKKkzLzEokoAaXMI1A==","f_longblob":null,"f_tinyblob":"dGlueWJsb2I=","f_varbinary":"SGVsbG8gd29ybGQ=","f_smallint":12345,"f_smallint_unsigned":54321,"f_mediumint":123456,"f_mediumint_unsigned":654321,"f_int":1234567,"f_int_unsigned":7654321,"f_integer":1234567,"f_integer_unsigned":7654321,"f_bigint":123456789,"f_bigint_unsigned":987654321,"f_numeric":123,"f_decimal":789,"f_float":12.34000015258789,"f_double":56.78,"f_double_precision":90.12,"f_longtext":"This is a long text field","f_mediumtext":"This is a medium text field","f_text":"This is a text field","f_tinytext":"This is a tiny text field","f_varchar":"This is a varchar field","f_date":19109,"f_datetime":1651069800000,"f_timestamp":"2023-04-27T03:08:40Z","f_bit1":true,"f_bit64":"VVVVVVVVVVU=","f_char":"C","f_enum":"enum2","f_mediumblob":"GwAAAHicC8nILFYAokSF3NSUzNJchaSc/CSFtMzUnBQAg/8Jmg==","f_long_varchar":"This is a long varchar field","f_real":12.345,"f_time":52200000000,"f_tinyint":-128,"f_tinyint_unsigned":255,"f_json":"{\"key\": \"value\"}","f_year":2022},"source":{"version":"1.6.4.Final","connector":"mysql","name":"mysql_cdc_1","ts_ms":0,"snapshot":"false","db":"mysql_cdc","sequence":null,"table":"mysql_cdc_e2e_source_table","server_id":0,"gtid":null,"file":"","pos":0,"row":0,"thread":null,"query":null},"op":"r","ts_ms":1700215102194,"transaction":null}}
{"schema":{"type":"struct","fields":[{"type":"struct","fields":[{"type":"int32","optional":false,"field":"id"},{"type":"bytes","optional":true,"field":"f_binary"},{"type":"bytes","optional":true,"field":"f_blob"},{"type":"bytes","optional":true,"field":"f_long_varbinary"},{"type":"bytes","optional":true,"field":"f_longblob"},{"type":"bytes","optional":true,"field":"f_tinyblob"},{"type":"bytes","optional":true,"field":"f_varbinary"},{"type":"int16","optional":true,"field":"f_smallint"},{"type":"int32","optional":true,"field":"f_smallint_unsigned"},{"type":"int32","optional":true,"field":"f_mediumint"},{"type":"int32","optional":true,"field":"f_mediumint_unsigned"},{"type":"int32","optional":true,"field":"f_int"},{"type":"int64","optional":true,"field":"f_int_unsigned"},{"type":"int32","optional":true,"field":"f_integer"},{"type":"int64","optional":true,"field":"f_integer_unsigned"},{"type":"int64","optional":true,"field":"f_bigint"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0"},"field":"f_bigint_unsigned"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_numeric"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_decimal"},{"type":"double","optional":true,"field":"f_float"},{"type":"double","optional":true,"field":"f_double"},{"type":"double","optional":true,"field":"f_double_precision"},{"type":"string","optional":true,"field":"f_longtext"},{"type":"string","optional":true,"field":"f_mediumtext"},{"type":"string","optional":true,"field":"f_text"},{"type":"string","optional":true,"field":"f_tinytext"},{"type":"string","optional":true,"field":"f_varchar"},{"type":"int32","optional":true,"name":"io.debezium.time.Date","version":1,"field":"f_date"},{"type":"int64","optional":true,"name":"io.debezium.time.Timestamp","version":1,"field":"f_datetime"},{"type":"string","optional":true,"name":"io.debezium.time.ZonedTimestamp","version":1,"field":"f_timestamp"},{"type":"boolean","optional":true,"field":"f_bit1"},{"type":"bytes","optional":true,"name":"io.debezium.data.Bits","version":1,"parameters":{"length":"64"},"field":"f_bit64"},{"type":"string","optional":true,"field":"f_char"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"enum1,enum2,enum3"},"field":"f_enum"},{"type":"bytes","optional":true,"field":"f_mediumblob"},{"type":"string","optional":true,"field":"f_long_varchar"},{"type":"double","optional":true,"field":"f_real"},{"type":"int64","optional":true,"name":"io.debezium.time.MicroTime","version":1,"field":"f_time"},{"type":"int16","optional":true,"field":"f_tinyint"},{"type":"int16","optional":true,"field":"f_tinyint_unsigned"},{"type":"string","optional":true,"name":"io.debezium.data.Json","version":1,"field":"f_json"},{"type":"int32","optional":true,"name":"io.debezium.time.Year","version":1,"field":"f_year"}],"optional":true,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Value","field":"before"},{"type":"struct","fields":[{"type":"int32","optional":false,"field":"id"},{"type":"bytes","optional":true,"field":"f_binary"},{"type":"bytes","optional":true,"field":"f_blob"},{"type":"bytes","optional":true,"field":"f_long_varbinary"},{"type":"bytes","optional":true,"field":"f_longblob"},{"type":"bytes","optional":true,"field":"f_tinyblob"},{"type":"bytes","optional":true,"field":"f_varbinary"},{"type":"int16","optional":true,"field":"f_smallint"},{"type":"int32","optional":true,"field":"f_smallint_unsigned"},{"type":"int32","optional":true,"field":"f_mediumint"},{"type":"int32","optional":true,"field":"f_mediumint_unsigned"},{"type":"int32","optional":true,"field":"f_int"},{"type":"int64","optional":true,"field":"f_int_unsigned"},{"type":"int32","optional":true,"field":"f_integer"},{"type":"int64","optional":true,"field":"f_integer_unsigned"},{"type":"int64","optional":true,"field":"f_bigint"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0"},"field":"f_bigint_unsigned"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_numeric"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_decimal"},{"type":"double","optional":true,"field":"f_float"},{"type":"double","optional":true,"field":"f_double"},{"type":"double","optional":true,"field":"f_double_precision"},{"type":"string","optional":true,"field":"f_longtext"},{"type":"string","optional":true,"field":"f_mediumtext"},{"type":"string","optional":true,"field":"f_text"},{"type":"string","optional":true,"field":"f_tinytext"},{"type":"string","optional":true,"field":"f_varchar"},{"type":"int32","optional":true,"name":"io.debezium.time.Date","version":1,"field":"f_date"},{"type":"int64","optional":true,"name":"io.debezium.time.Timestamp","version":1,"field":"f_datetime"},{"type":"string","optional":true,"name":"io.debezium.time.ZonedTimestamp","version":1,"field":"f_timestamp"},{"type":"boolean","optional":true,"field":"f_bit1"},{"type":"bytes","optional":true,"name":"io.debezium.data.Bits","version":1,"parameters":{"length":"64"},"field":"f_bit64"},{"type":"string","optional":true,"field":"f_char"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"enum1,enum2,enum3"},"field":"f_enum"},{"type":"bytes","optional":true,"field":"f_mediumblob"},{"type":"string","optional":true,"field":"f_long_varchar"},{"type":"double","optional":true,"field":"f_real"},{"type":"int64","optional":true,"name":"io.debezium.time.MicroTime","version":1,"field":"f_time"},{"type":"int16","optional":true,"field":"f_tinyint"},{"type":"int16","optional":true,"field":"f_tinyint_unsigned"},{"type":"string","optional":true,"name":"io.debezium.data.Json","version":1,"field":"f_json"},{"type":"int32","optional":true,"name":"io.debezium.time.Year","version":1,"field":"f_year"}],"optional":true,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Value","field":"after"},{"type":"struct","fields":[{"type":"string","optional":false,"field":"version"},{"type":"string","optional":false,"field":"connector"},{"type":"string","optional":false,"field":"name"},{"type":"int64","optional":false,"field":"ts_ms"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"true,last,false"},"default":"false","field":"snapshot"},{"type":"string","optional":false,"field":"db"},{"type":"string","optional":true,"field":"sequence"},{"type":"string","optional":true,"field":"table"},{"type":"int64","optional":false,"field":"server_id"},{"type":"string","optional":true,"field":"gtid"},{"type":"string","optional":false,"field":"file"},{"type":"int64","optional":false,"field":"pos"},{"type":"int32","optional":false,"field":"row"},{"type":"int64","optional":true,"field":"thread"},{"type":"string","optional":true,"field":"query"}],"optional":false,"name":"io.debezium.connector.mysql.Source","field":"source"},{"type":"string","optional":false,"field":"op"},{"type":"int64","optional":true,"field":"ts_ms"},{"type":"struct","fields":[{"type":"string","optional":false,"field":"id"},{"type":"int64","optional":false,"field":"total_order"},{"type":"int64","optional":false,"field":"data_collection_order"}],"optional":true,"field":"transaction"}],"optional":false,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Envelope"},"payload":{"before":null,"after":{"id":2,"f_binary":"YWJjdAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==","f_blob":"aGVsbG8=","f_long_varbinary":"GAAAAHicC8nILFYAokSFnPy8dIWyxKKkzLzEokoAaXMI1A==","f_longblob":null,"f_tinyblob":"dGlueWJsb2I=","f_varbinary":"SGVsbG8gd29ybGQ=","f_smallint":12345,"f_smallint_unsigned":54321,"f_mediumint":123456,"f_mediumint_unsigned":654321,"f_int":1234567,"f_int_unsigned":7654321,"f_integer":1234567,"f_integer_unsigned":7654321,"f_bigint":123456789,"f_bigint_unsigned":987654321,"f_numeric":123,"f_decimal":789,"f_float":12.34000015258789,"f_double":56.78,"f_double_precision":90.12,"f_longtext":"This is a long text field","f_mediumtext":"This is a medium text field","f_text":"This is a text field","f_tinytext":"This is a tiny text field","f_varchar":"This is a varchar field","f_date":19109,"f_datetime":1651069800000,"f_timestamp":"2023-04-27T03:08:40Z","f_bit1":true,"f_bit64":"VVVVVVVVVVU=","f_char":"C","f_enum":"enum2","f_mediumblob":"GwAAAHicC8nILFYAokSF3NSUzNJchaSc/CSFtMzUnBQAg/8Jmg==","f_long_varchar":"This is a long varchar field","f_real":112.345,"f_time":52200000000,"f_tinyint":-128,"f_tinyint_unsigned":22,"f_json":"{\"key\": \"value\"}","f_year":2013},"source":{"version":"1.6.4.Final","connector":"mysql","name":"mysql_cdc_1","ts_ms":0,"snapshot":"false","db":"mysql_cdc","sequence":null,"table":"mysql_cdc_e2e_source_table","server_id":0,"gtid":null,"file":"","pos":0,"row":0,"thread":null,"query":null},"op":"r","ts_ms":1700215102195,"transaction":null}}
{"schema":{"type":"struct","fields":[{"type":"struct","fields":[{"type":"int32","optional":false,"field":"id"},{"type":"bytes","optional":true,"field":"f_binary"},{"type":"bytes","optional":true,"field":"f_blob"},{"type":"bytes","optional":true,"field":"f_long_varbinary"},{"type":"bytes","optional":true,"field":"f_longblob"},{"type":"bytes","optional":true,"field":"f_tinyblob"},{"type":"bytes","optional":true,"field":"f_varbinary"},{"type":"int16","optional":true,"field":"f_smallint"},{"type":"int32","optional":true,"field":"f_smallint_unsigned"},{"type":"int32","optional":true,"field":"f_mediumint"},{"type":"int32","optional":true,"field":"f_mediumint_unsigned"},{"type":"int32","optional":true,"field":"f_int"},{"type":"int64","optional":true,"field":"f_int_unsigned"},{"type":"int32","optional":true,"field":"f_integer"},{"type":"int64","optional":true,"field":"f_integer_unsigned"},{"type":"int64","optional":true,"field":"f_bigint"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0"},"field":"f_bigint_unsigned"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_numeric"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_decimal"},{"type":"double","optional":true,"field":"f_float"},{"type":"double","optional":true,"field":"f_double"},{"type":"double","optional":true,"field":"f_double_precision"},{"type":"string","optional":true,"field":"f_longtext"},{"type":"string","optional":true,"field":"f_mediumtext"},{"type":"string","optional":true,"field":"f_text"},{"type":"string","optional":true,"field":"f_tinytext"},{"type":"string","optional":true,"field":"f_varchar"},{"type":"int32","optional":true,"name":"io.debezium.time.Date","version":1,"field":"f_date"},{"type":"int64","optional":true,"name":"io.debezium.time.Timestamp","version":1,"field":"f_datetime"},{"type":"string","optional":true,"name":"io.debezium.time.ZonedTimestamp","version":1,"field":"f_timestamp"},{"type":"boolean","optional":true,"field":"f_bit1"},{"type":"bytes","optional":true,"name":"io.debezium.data.Bits","version":1,"parameters":{"length":"64"},"field":"f_bit64"},{"type":"string","optional":true,"field":"f_char"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"enum1,enum2,enum3"},"field":"f_enum"},{"type":"bytes","optional":true,"field":"f_mediumblob"},{"type":"string","optional":true,"field":"f_long_varchar"},{"type":"double","optional":true,"field":"f_real"},{"type":"int64","optional":true,"name":"io.debezium.time.MicroTime","version":1,"field":"f_time"},{"type":"int16","optional":true,"field":"f_tinyint"},{"type":"int16","optional":true,"field":"f_tinyint_unsigned"},{"type":"string","optional":true,"name":"io.debezium.data.Json","version":1,"field":"f_json"},{"type":"int32","optional":true,"name":"io.debezium.time.Year","version":1,"field":"f_year"}],"optional":true,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Value","field":"before"},{"type":"struct","fields":[{"type":"int32","optional":false,"field":"id"},{"type":"bytes","optional":true,"field":"f_binary"},{"type":"bytes","optional":true,"field":"f_blob"},{"type":"bytes","optional":true,"field":"f_long_varbinary"},{"type":"bytes","optional":true,"field":"f_longblob"},{"type":"bytes","optional":true,"field":"f_tinyblob"},{"type":"bytes","optional":true,"field":"f_varbinary"},{"type":"int16","optional":true,"field":"f_smallint"},{"type":"int32","optional":true,"field":"f_smallint_unsigned"},{"type":"int32","optional":true,"field":"f_mediumint"},{"type":"int32","optional":true,"field":"f_mediumint_unsigned"},{"type":"int32","optional":true,"field":"f_int"},{"type":"int64","optional":true,"field":"f_int_unsigned"},{"type":"int32","optional":true,"field":"f_integer"},{"type":"int64","optional":true,"field":"f_integer_unsigned"},{"type":"int64","optional":true,"field":"f_bigint"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0"},"field":"f_bigint_unsigned"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_numeric"},{"type":"bytes","optional":true,"name":"org.apache.kafka.connect.data.Decimal","version":1,"parameters":{"scale":"0","connect.decimal.precision":"10"},"field":"f_decimal"},{"type":"double","optional":true,"field":"f_float"},{"type":"double","optional":true,"field":"f_double"},{"type":"double","optional":true,"field":"f_double_precision"},{"type":"string","optional":true,"field":"f_longtext"},{"type":"string","optional":true,"field":"f_mediumtext"},{"type":"string","optional":true,"field":"f_text"},{"type":"string","optional":true,"field":"f_tinytext"},{"type":"string","optional":true,"field":"f_varchar"},{"type":"int32","optional":true,"name":"io.debezium.time.Date","version":1,"field":"f_date"},{"type":"int64","optional":true,"name":"io.debezium.time.Timestamp","version":1,"field":"f_datetime"},{"type":"string","optional":true,"name":"io.debezium.time.ZonedTimestamp","version":1,"field":"f_timestamp"},{"type":"boolean","optional":true,"field":"f_bit1"},{"type":"bytes","optional":true,"name":"io.debezium.data.Bits","version":1,"parameters":{"length":"64"},"field":"f_bit64"},{"type":"string","optional":true,"field":"f_char"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"enum1,enum2,enum3"},"field":"f_enum"},{"type":"bytes","optional":true,"field":"f_mediumblob"},{"type":"string","optional":true,"field":"f_long_varchar"},{"type":"double","optional":true,"field":"f_real"},{"type":"int64","optional":true,"name":"io.debezium.time.MicroTime","version":1,"field":"f_time"},{"type":"int16","optional":true,"field":"f_tinyint"},{"type":"int16","optional":true,"field":"f_tinyint_unsigned"},{"type":"string","optional":true,"name":"io.debezium.data.Json","version":1,"field":"f_json"},{"type":"int32","optional":true,"name":"io.debezium.time.Year","version":1,"field":"f_year"}],"optional":true,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Value","field":"after"},{"type":"struct","fields":[{"type":"string","optional":false,"field":"version"},{"type":"string","optional":false,"field":"connector"},{"type":"string","optional":false,"field":"name"},{"type":"int64","optional":false,"field":"ts_ms"},{"type":"string","optional":true,"name":"io.debezium.data.Enum","version":1,"parameters":{"allowed":"true,last,false"},"default":"false","field":"snapshot"},{"type":"string","optional":false,"field":"db"},{"type":"string","optional":true,"field":"sequence"},{"type":"string","optional":true,"field":"table"},{"type":"int64","optional":false,"field":"server_id"},{"type":"string","optional":true,"field":"gtid"},{"type":"string","optional":false,"field":"file"},{"type":"int64","optional":false,"field":"pos"},{"type":"int32","optional":false,"field":"row"},{"type":"int64","optional":true,"field":"thread"},{"type":"string","optional":true,"field":"query"}],"optional":false,"name":"io.debezium.connector.mysql.Source","field":"source"},{"type":"string","optional":false,"field":"op"},{"type":"int64","optional":true,"field":"ts_ms"},{"type":"struct","fields":[{"type":"string","optional":false,"field":"id"},{"type":"int64","optional":false,"field":"total_order"},{"type":"int64","optional":false,"field":"data_collection_order"}],"optional":true,"field":"transaction"}],"optional":false,"name":"mysql_cdc_1.mysql_cdc.mysql_cdc_e2e_source_table.Envelope"},"payload":{"before":null,"after":{"id":3,"f_binary":"YWJjdAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==","f_blob":"aGVsbG8=","f_long_varbinary":"GAAAAHicC8nILFYAokSFnPy8dIWyxKKkzLzEokoAaXMI1A==","f_longblob":null,"f_tinyblob":"dGlueWJsb2I=","f_varbinary":"SGVsbG8gd29ybGQ=","f_smallint":12345,"f_smallint_unsigned":54321,"f_mediumint":123456,"f_mediumint_unsigned":654321,"f_int":1234567,"f_int_unsigned":7654321,"f_integer":1234567,"f_integer_unsigned":7654321,"f_bigint":123456789,"f_bigint_unsigned":987654321,"f_numeric":123,"f_decimal":789,"f_float":12.34000015258789,"f_double":56.78,"f_double_precision":90.12,"f_longtext":"This is a long text field","f_mediumtext":"This is a medium text field","f_text":"This is a text field","f_tinytext":"This is a tiny text field","f_varchar":"This is a varchar field","f_date":19109,"f_datetime":1651069800000,"f_timestamp":"2023-04-27T03:08:40Z","f_bit1":true,"f_bit64":"VVVVVVVVVVU=","f_char":"C","f_enum":"enum2","f_mediumblob":"GwAAAHicC8nILFYAokSF3NSUzNJchaSc/CSFtMzUnBQAg/8Jmg==","f_long_varchar":"This is a long varchar field","f_real":112.345,"f_time":52200000000,"f_tinyint":-128,"f_tinyint_unsigned":22,"f_json":"{\"key\": \"value\"}","f_year":2021},"source":{"version":"1.6.4.Final","connector":"mysql","name":"mysql_cdc_1","ts_ms":0,"snapshot":"false","db":"mysql_cdc","sequence":null,"table":"mysql_cdc_e2e_source_table","server_id":0,"gtid":null,"file":"","pos":0,"row":0,"thread":null,"query":null},"op":"r","ts_ms":1700215102196,"transaction":null}}