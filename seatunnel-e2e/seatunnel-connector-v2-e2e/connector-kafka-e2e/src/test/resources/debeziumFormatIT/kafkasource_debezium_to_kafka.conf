#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"

  #spark config
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 1
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  Kafka {
    bootstrap.servers = "kafka_e2e:9092"
    topic = "dbserver1.debezium.products"
    start_mode = earliest
    format = debezium_json
    schema = {
      fields {
        id = "int"
        f_binary = "bytes"
        f_blob = "bytes"
        f_long_varbinary = "bytes"
        f_longblob = "bytes"
        f_tinyblob = "bytes"
        f_varbinary = "string"
        f_smallint = "smallint"
        f_smallint_unsigned = "int"
        f_mediumint = "int"
        f_mediumint_unsigned = "int"
        f_int = "int"
        f_int_unsigned = "bigint"
        f_integer = "int"
        f_integer_unsigned = "bigint"
        f_bigint = "bigint"
        f_bigint_unsigned = "decimal(10, 0)"
        f_numeric = "decimal(10, 0)"
        f_decimal = "decimal(10, 0)"
        f_float = "float"
        f_double = "double"
        f_double_precision = "double"
        f_longtext = "string"
        f_mediumtext = "string"
        f_text = "string"
        f_tinytext = "string"
        f_varchar = "string"
        f_date = "date"
        f_datetime = "timestamp"
        f_timestamp = "timestamp"
        f_bit1 = "boolean"
        f_bit64 = "tinyint"
        f_char = "string"
        f_enum = "string"
        f_mediumblob = "bytes"
        f_long_varchar = "string"
        f_real = "double"
        f_time = "time"
        f_tinyint = "tinyint"
        f_tinyint_unsigned = "int"
        f_json = "string"
        f_year = "int"
      }
    }
  }
}

sink {
  Kafka {
    bootstrap.servers = "kafka_e2e:9092"
    topic = "test-debezium-sink"
    format = debezium_json
    partition = 0
  }
}