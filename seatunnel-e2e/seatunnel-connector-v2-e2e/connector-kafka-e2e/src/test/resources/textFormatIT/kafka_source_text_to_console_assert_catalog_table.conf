#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"

  # You can set spark configuration here
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 1
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  Kafka {
    bootstrap.servers = "kafkaCluster:9092"
    topic = "test_topic_text"
    result_table_name = "kafka_table"
    kafka.auto.offset.reset = "earliest"
    format_error_handle_way = fail
    schema = {
      columns = [
        {
              name = id
              type = bigint
        }
        {
              name = c_map
              type = "map<string, smallint>"
        }
        {
              name = c_array
              type = "array<tinyint>"
        }
        {
              name = c_string
              type = "string"
        }
        {
              name = c_boolean
              type = "boolean"
        }
        {
              name = c_tinyint
              type = "tinyint"
        }
        {
              name = c_smallint
              type = "smallint"
        }
        {
              name = c_int
              type = "int"
        }
        {
              name = c_bigint
              type = "bigint"
        }
        {
              name = c_float
              type = "float"
        }
        {
              name = c_double
              type = "double"
        }
        {
              name = c_decimal
              type = "decimal(2, 1)"
        }
        {
              name = c_bytes
              type = "bytes"
        }
        {
              name = c_date
              type = "date"
        }
        {
              name = c_timestamp
              type = "timestamp"
        }
      ]
      primaryKey = {
        name = "primary key"
        columnNames = ["id"]
      }
      constraintKeys = [
        {
            constraintName = "unique_c_string"
            constraintType = UNIQUE_KEY
            constraintColumns = [
                {
                    columnName = "c_string"
                    sortType = ASC
                }
            ]
        }
     ]
    }
    format = text
    field_delimiter = ","
  }
}

sink {
  Assert {
    source_table_name = "kafka_table"
    rules =
      {
        field_rules = [
          {
            field_name = id
            field_type = bigint
            field_value = [
              {
                rule_type = NOT_NULL
              },
              {
                rule_type = MIN
                rule_value = 0
              },
              {
                rule_type = MAX
                rule_value = 99
              }
            ]
          }
        ]
      catalog_table_rule = {
        primary_key_rule = {
            primary_key_name = "primary key"
            primary_key_columns = ["id"]
        }
        constraint_key_rule = [
            {
            constraint_key_name = "unique_c_string"
            constraint_key_type = UNIQUE_KEY
            constraint_key_columns = [
                {
                    constraint_key_column_name = "c_string"
                    constraint_key_sort_type = ASC
                }
            ]
            }
        ]
      }
    }
  }
}