#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 2
  job.mode = "BATCH"
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  TDengine {
    url: "jdbc:TAOS-RS://flink_e2e_tdengine_src:6041/"
    username: "root"
    password: "taosdata"
    database: "power"
    stable: "meters"
    lower_bound: "2018-10-03 14:38:05.000"
    upper_bound: "2018-10-03 14:38:16.801"
    result_table_name = "tdengine_result"
  }
  # If you would like to get more information about how to configure seatunnel and see full list of source plugins,
  # please go to https://seatunnel.apache.org/docs/category/source-v2
}

transform {
}

sink {
  TDengine {
    url: "jdbc:TAOS-RS://flink_e2e_tdengine_sink:6041/"
    username: "root"
    password: "taosdata"
    database: "power2"
    stable: "meters2"
    timezone: "UTC"
  }
  # If you would like to get more information about how to configure seatunnel and see full list of sink plugins,
  # please go to https://seatunnel.apache.org/docs/category/sink-v2
}
