#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
  #spark config
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 1
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  Pulsar {
    client.service-url = "pulsar://pulsar.batch.e2e:6650"
    admin.service-url = "http://pulsar.batch.e2e:8080"
    subscription.name = "e2e"
    topic = "topic-it"
    cursor.startup.mode = "EARLIEST"
    cursor.stop.mode = "LATEST"
    format = json
    result_table_name = "pulsar_canal"
    schema = {
      fields {
        c_map = "map<string, string>"
        c_array = "array<int>"
        c_string = string
        c_boolean = boolean
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
        c_decimal = "decimal(30, 8)"
        c_bytes = bytes
        c_date = date
        c_timestamp = timestamp
      }
    }
  }
}

sink {
  Assert {
    rules =
      {
        field_rules = [
          {
            field_name = c_string
            field_type = string
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_boolean
            field_type = boolean
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_float
            field_type = float
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_double
            field_type = double
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_tinyint
            field_type = tinyint
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_smallint
            field_type = smallint
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_int
            field_type = int
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_bigint
            field_type = bigint
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_date
            field_type = date
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
          {
            field_name = c_timestamp
            field_type = timestamp
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          }
        ]
      }
  }
}
