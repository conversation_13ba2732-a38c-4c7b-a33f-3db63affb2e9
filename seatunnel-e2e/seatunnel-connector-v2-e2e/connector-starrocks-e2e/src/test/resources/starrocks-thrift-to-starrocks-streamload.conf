#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  StarRocks {
    nodeUrls = ["starrocks_e2e:8030"]
    username = root
    password = ""
    database = "test"
    table = "e2e_table_source"
    max_retries = 3
    schema {
      fields {
        BIGINT_COL = BIGINT
        LARGEINT_COL = STRING
        SMALLINT_COL = SMALLINT
        TINYINT_COL = TINYINT
        BOOLEAN_COL = BOOLEAN
        DECIMAL_COL = "DECIMAL(20, 1)"
        DOUBLE_COL = DOUBLE
        FLOAT_COL = FLOAT
        INT_COL = INT
        CHAR_COL = STRING
        VARCHAR_11_COL = STRING
        STRING_COL = STRING
        DATETIME_COL = TIMESTAMP
        DATE_COL = DATE
      }
    }
  }
}

transform {
}

sink {
  StarRocks {
    nodeUrls = ["starrocks_e2e:8030"]
    username = root
    password = ""
    database = "test"
    table = "e2e_table_sink"
    batch_max_rows = 100
    max_retries = 3
    base-url="************************************"
    starrocks.config = {
      format = "JSON"
      strip_outer_array = true
    }
    "schema_save_mode"="RECREATE_SCHEMA"
    "data_save_mode"="APPEND_DATA"
    save_mode_create_template = "CREATE TABLE IF NOT EXISTS `${database}`.`${table_name}` (\n ${rowtype_fields}\n ) ENGINE=OLAP \n  DUPLICATE KEY(`BIGINT_COL`) \n  DISTRIBUTED BY HASH (BIGINT_COL) BUCKETS 1 \n PROPERTIES (\n   \"replication_num\" = \"1\", \n  \"in_memory\" = \"false\" , \n  \"storage_format\" = \"DEFAULT\"  \n )"

  }
}