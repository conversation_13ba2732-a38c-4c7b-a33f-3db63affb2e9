#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"

  # You can set spark configuration here
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 1
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  Neo4j {
    uri = "neo4j://neo4j-host:7687"
    username = "neo4j"
    password = "Test@12343"
    database = "neo4j"

    max_transaction_retry_time = 1
    max_connection_timeout = 1

    query = "MATCH (t:Test) WITH *, t{.int} AS _map RETURN t.string, t.boolean, t.long, t.double, t.byteArray, t.date, t.localDateTime, _map, t.list, t.int, t.float"

    schema {
      fields {
        t.string = STRING
        t.boolean = BOOLEAN
        t.long = BIGINT
        t.double = DOUBLE
        t.null = NULL
        t.byteArray = BYTES
        t.date = DATE
        t.localDateTime = TIMESTAMP
        _map = "MAP<STRING, INT>"
        t.list = "ARRAY<INT>"
        t.int = INT
        t.float = FLOAT
      }
    }
  }
}

transform {
}

sink {
  Neo4j {
    uri = "neo4j://neo4j-host:7687"
    username = "neo4j"
    password = "Test@12343"
    database = "neo4j"

    max_transaction_retry_time = 1
    max_connection_timeout = 1

    query = "CREATE (tt:TestTest {string:$string, boolean:$boolean, long:$long, double:$double, byteArray:$byteArray, date:date($date), localDateTime:localdatetime($localDateTime), list:$list, int:$int, float:$float, mapValue:$map['int']})"
    queryParamPosition = {
      string = 0
      boolean = 1
      long = 2
      double = 3
      byteArray = 5
      date = 6
      localDateTime = 7
      map = 8
      list = 9
      int = 10
      float = 11
    }
  }
}