#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
}

source {
  Rocketmq {
    name.srv.addr = "rocketmq-e2e:9876"
    topics = "test_topic_group"
    result_table_name = "rocketmq_table"
    format = json
    start.mode = "CONSUME_FROM_GROUP_OFFSETS"
    schema = {
      fields {
        id = bigint
      }
    }
  }
}

transform {
}

sink {
  Assert {
    source_table_name = "rocketmq_table"
    rules = {
      field_rules = [
        {
          field_name = id
          field_type = bigint
          field_value = [

            {
              rule_type = MIN
              rule_value = 100
            },
            {
              rule_type = MAX
              rule_value = 149
            }
          ]
        }
      ]
    }
  }
}