#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
  #spark config
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 1
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  MongoDB {
    uri = "mongodb://e2e_mongodb:27017/test_db"
    database = "test_db"
    collection = "test_flat_table"
    result_table_name = "mongodb_table"
    flat.sync-string = true
    schema = {
      fields {
        data = string
      }
    }
  }
}

sink {
  Assert {
    source_table_name = "mongodb_table"
    rules {
      row_rules = [
        {
          rule_type = MAX_ROW
          rule_value = 1
        },
        {
          rule_type = MIN_ROW
          rule_value = 1
        }
      ],
      field_rules = [
        {
          field_name = data
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }
      ]
    }
  }
}