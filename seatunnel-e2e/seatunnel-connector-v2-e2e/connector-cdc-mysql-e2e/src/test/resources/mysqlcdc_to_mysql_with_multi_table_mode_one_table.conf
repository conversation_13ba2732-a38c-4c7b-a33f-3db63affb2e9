#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  # You can set engine configuration here
  parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 5000
}

source {
  MySQL-CDC {
    result_table_name = "customers_mysql_cdc"
    server-id = 5652
    username = "mysqluser"
    password = "mysqlpw"
    table-names = ["mysql_cdc.mysql_cdc_e2e_source_table"]
    base-url = "*****************************************"
  }
}

transform {
}

sink {
  jdbc {
    source_table_name = "customers_mysql_cdc"
    url = "******************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    user = "mysqluser"
    password = "mysqlpw"
    database = "mysql_cdc2"
    generate_sink_sql = true
  }
}