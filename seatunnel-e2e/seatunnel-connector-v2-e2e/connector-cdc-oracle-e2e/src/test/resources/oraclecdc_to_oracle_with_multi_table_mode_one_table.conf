#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  # You can set engine configuration here
  execution.parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 5000
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  Oracle-CDC {
    result_table_name = "customers"
    username = "system"
    password = "oracle"
    database-names = ["XE"]
    schema-names = ["DEBEZIUM"]
    table-names = ["XE.DEBEZIUM.FULL_TYPES"]
    base-url = "**************************************************"
    source.reader.close.timeout = 120000
    connection.pool.size = 1
    debezium {
      #  log.mining.strategy = "online_catalog"
      #  log.mining.continuous.mine = true
        database.oracle.jdbc.timezoneAsRegion = "false"
    }
  }
}

transform {
}

sink {
Jdbc {
  source_table_name = "customers"
  driver = "oracle.jdbc.driver.OracleDriver"
  url = "**************************************************"
  user = "system"
  password = "oracle"
  generate_sink_sql = true
  tablePrefix = "SINK_"
  database = "XE"
  schema = "DEBEZIUM"
  batch_size = 1
}
}