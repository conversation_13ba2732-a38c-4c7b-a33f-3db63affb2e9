#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = BATCH
  # checkpoint.interval = 10000
}

source {
  FakeSource {
    row.num = 1
    schema = {
      fields {
        c_array = "array<int>"
        c_map = "map<string, string>"
        c_row = {
          c_string = string
          c_boolean = boolean
          c_tinyint = tinyint
          c_smallint = smallint
          c_int = int
          c_bigint = bigint
          c_float = float
          c_double = double
          c_decimal = "decimal(30, 8)"
          c_date = date
          c_timestamp = timestamp
        }
      }
    }
    rows = [
      {
        kind = INSERT
        fields = [[0, 1, 2], { k0 = v0 }, ["AAA", false, 1, 1, 333, 323232, 3.1, 9.33333, 99999.99999999, "2012-12-21", "2012-12-21T12:34:56"]]
      }
    ]
    result_table_name = "fake"
  }
}

sink{
  Assert {
    source_table_name = "fake"
    rules =
      {
        row_rules = [
          {
            rule_type = MAX_ROW
            rule_value = 1
          },
          {
            rule_type = MIN_ROW
            rule_value = 1
          }
        ],
        field_rules = [
        {
            field_name = c_array
            field_type = "array<int>"
            field_value = [
                {
                    equals_to = [0, 1, 2]
                }
            ]
        },
        {
          field_name = c_map
          field_type = "map<string, string>"
          field_value = [
            {
              equals_to = { k0 = v0 }
            }
          ]
        },
        {
          field_name = c_row
          field_type = {
            c_string = string
            c_boolean = boolean
            c_tinyint = tinyint
            c_smallint = smallint
            c_int = int
            c_bigint = bigint
            c_float = float
            c_double = double
            c_decimal = "decimal(30, 8)"
            c_date = date
            c_timestamp = timestamp
          }
          field_value = [
            {
              equals_to = ["AAA", false, 1, 1, 333, 323232, 3.1, 9.33333, 99999.99999999, "2012-12-21", "2012-12-21T12:34:56"]
            }
          ]
        }
        ]
      }
  }
}