#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

source_table = """
set allow_experimental_geo_types = 1;
create table if not exists `default`.source_table(
    `id`                Int64,
    `c_map`             Map(String, Int32),
    `c_array_string`    Array(String),
    `c_array_short`     Array(Int16),
    `c_array_int`       Array(Int32),
    `c_array_long`      Array(Int64),
    `c_array_float`     Array(Float32),
    `c_array_double`    Array(Float64),
    `c_string`          String,
    `c_boolean`         Boolean,
    `c_int8`            Int8,
    `c_int16`           Int16,
    `c_int32`           Int32,
    `c_int64`           Int64,
    `c_float32`         Float32,
    `c_float64`         Float64,
    `c_decimal`         Decimal(9,4),
    `c_date`            Date,
    `c_datetime`        DateTime64,
    `c_nullable`        Nullable(Int32),
    `c_lowcardinality`  LowCardinality(String),
    `c_nested`          Nested
        (
            `int` UInt32,
            `double` Float64,
            `string` String
        ),
    `c_int128`          Int128,
    `c_uint128`         UInt128,
    `c_int256`          Int256,
    `c_uint256`         UInt256,
    `c_point`           Point,
    `c_ring`            Ring
)engine=Memory;
"""

sink_table = """
create table if not exists `default`.sink_table(
     `id`                Int64,
     `c_map`             Map(String, Int32),
     `c_array_string`    Array(String),
     `c_array_short`     Array(Int16),
     `c_array_int`       Array(Int32),
     `c_array_long`      Array(Int64),
     `c_array_float`     Array(Float32),
     `c_array_double`    Array(Float64),
     `c_string`          String,
     `c_boolean`         Boolean,
     `c_int8`            Int8,
     `c_int16`           Int16,
     `c_int32`           Int32,
     `c_int64`           Int64,
     `c_float32`         Float32,
     `c_float64`         Float64,
     `c_decimal`         Decimal(9,4),
     `c_date`            Date,
     `c_datetime`        DateTime64,
     `c_nullable`        Nullable(Int32),
     `c_lowcardinality`  LowCardinality(String),
     `c_nested`          Nested
             (
                 `int` UInt32,
                 `double` Float64,
                 `string` String
             ),
     `c_int128`          Int128,
     `c_uint128`         UInt128,
     `c_int256`          Int256,
     `c_uint256`         UInt256,
     `c_point`           Point,
     `c_ring`            Ring
)engine=Memory;
"""

insert_sql = """
insert into `default`.source_table
(
    `id`,
    `c_map`,
    `c_array_string`,
    `c_array_short`,
    `c_array_int`,
    `c_array_long`,
    `c_array_float`,
    `c_array_double`,
    `c_string`,
    `c_boolean`,
    `c_int8`,
    `c_int16`,
    `c_int32`,
    `c_int64`,
    `c_float32`,
    `c_float64`,
    `c_decimal`,
    `c_date`,
    `c_datetime`,
    `c_nullable`,
    `c_lowcardinality`,
    `c_nested.int`,
    `c_nested.double`,
    `c_nested.string`,
    `c_int128`,
    `c_uint128`,
    `c_int256`,
    `c_uint256`,
    `c_point`,
    `c_ring`
)
values
(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
"""

compare_sql = """
select
    %s
 from (
    select * from default.source_table
union all
    select * from default.sink_table
    )
group by %s
having count(*) < 2
"""