### 创建Range分区表


-- 创建Range分区表
DROP TABLE IF EXISTS test.table_partition_range;
CREATE TABLE IF NOT EXISTS test.table_partition_range (
    id BIGINT COMMENT 'id'
   ,name STRING COMMENT '姓名'
   ,gender STRING COMMENT '性别'
   ,age INT COMMENT '年龄'
   ,hero STRING COMMENT '英雄角色'
   ,hero_skill_damage_score DOUBLE COMMENT '英雄技能伤害评分'
   ,partition_value DATE COMMENT '分区值(指定列)'
) 
-- ENGINE=OLAP -- 不指定(默认OLAP)
/* DUPLICATE KEY(`id`) */ -- 默认为明细模型(排序列系统自动选定了前 3 列)
COMMENT '分区表_range分区'
PARTITION BY RANGE(partition_value) () /* 本次演示不预建分区 */
DISTRIBUTED BY HASH(id) BUCKETS AUTO /* 自动分桶 */
PROPERTIES (
    -- 设置: 无排序列的默认明细模型
    "enable_duplicate_without_keys_by_default" = "true"
    -- 使用自动分桶推算的创建语法(该参数可选, 如果不设置默认 "estimate_partition_size" = "10G")
   ,"estimate_partition_size" = "2G"
)
;


### 分区操作


-- 查看分区
SHOW PARTITIONS FROM test.table_partition_range;

-- Step1
-- 先删除分区, 再新增分区
ALTER TABLE test.table_partition_range DROP PARTITION IF EXISTS p20240610;
ALTER TABLE test.table_partition_range ADD PARTITION IF NOT EXISTS p20240610 VALUES LESS THAN ('2024-06-11');

-- Step2
-- 如果是按照上面的方式已经创建分区的前提下, 再执行下面的新增分区语句, LESS THAN 会自动 判断上界
-- 这个时候会发现Step2执行后, 创建的分区与下面的指定具体的左闭右开区间创建的分区是一样的(前提这里的上下界偏移量是1天)
ALTER TABLE test.table_partition_range DROP PARTITION IF EXISTS p20240611;
ALTER TABLE test.table_partition_range ADD PARTITION IF NOT EXISTS p20240611 VALUES LESS THAN ('2024-06-12');

-- 增加一个指定上下界的分区(左闭右开区间)
ALTER TABLE test.table_partition_range DROP PARTITION IF EXISTS p20240611;
ALTER TABLE test.table_partition_range ADD PARTITION IF NOT EXISTS p20240611 VALUES [('2024-06-11'), ('2024-06-12'));


### INSERT [OVERWRITE/INTO] 操作


-- 插入单个分区
INSERT INTO test.table_partition_range PARTITION(p20240611)
SELECT
       id
      ,name
      ,gender
      ,age
      ,hero
      ,hero_skill_damage_score
      ,partition_value
FROM (
       SELECT 1001 AS id, '安吉拉' AS name, '女' AS gender, 18 AS age, '法师' AS hero, 120 AS hero_skill_damage_score, '2024-05-01' AS partition_value UNION ALL
       SELECT 1002 AS id, '鲁班' AS name, '男' AS gender, 16 AS age, '射手' AS hero, 90 AS hero_skill_damage_score, '2024-06-11' AS partition_value UNION ALL
       SELECT 1003 AS id, '干将莫邪' AS name, '女' AS gender, 18 AS age, '法师' AS hero, 150 AS hero_skill_damage_score, '2024-06-11' AS partition_value UNION ALL
       SELECT 1004 AS id, '孙悟空' AS name, '男' AS gender, 20 AS age, '刺客' AS hero, 100 AS hero_skill_damage_score, '2024-06-11' AS partition_value UNION ALL
       SELECT 1005 AS id, '孙膑' AS name, '男' AS gender, 20 AS age, '辅助' AS hero, 60 AS hero_skill_damage_score, '2024-05-20' AS partition_value UNION ALL
       SELECT 1006 AS id, '夏侯惇' AS name, '男' AS gender, 22 AS age, '坦克' AS hero, 80 AS hero_skill_damage_score, '2024-05-20' AS partition_value 
      ) t1
WHERE partition_value = '2024-06-11'
;

INSERT INTO test.table_partition_range PARTITION(p20240610)
SELECT
       id
      ,name
      ,gender
      ,age
      ,hero
      ,hero_skill_damage_score
      ,partition_value
FROM (
       SELECT 1001 AS id, '安吉拉' AS name, '女' AS gender, 18 AS age, '法师' AS hero, 120 AS hero_skill_damage_score, '2024-05-01' AS partition_value UNION ALL
       SELECT 1002 AS id, '鲁班' AS name, '男' AS gender, 16 AS age, '射手' AS hero, 90 AS hero_skill_damage_score, '2024-06-11' AS partition_value UNION ALL
       SELECT 1003 AS id, '干将莫邪' AS name, '女' AS gender, 18 AS age, '法师' AS hero, 150 AS hero_skill_damage_score, '2024-06-11' AS partition_value UNION ALL
       SELECT 1004 AS id, '孙悟空' AS name, '男' AS gender, 20 AS age, '刺客' AS hero, 100 AS hero_skill_damage_score, '2024-06-11' AS partition_value UNION ALL
       SELECT 1005 AS id, '孙膑' AS name, '男' AS gender, 20 AS age, '辅助' AS hero, 60 AS hero_skill_damage_score, '2024-05-20' AS partition_value UNION ALL
       SELECT 1006 AS id, '夏侯惇' AS name, '男' AS gender, 22 AS age, '坦克' AS hero, 80 AS hero_skill_damage_score, '2024-05-20' AS partition_value 
      ) t1
WHERE partition_value < '2024-06-11'
;


### SELECT-FROM-PARTITION (查询指定分区数据)

-- 查询单个分区的数据
SELECT * FROM test.table_partition_range PARTITION (p20240610);

-- 查询多个分区的数据(多个分区以逗号分隔)
SELECT * FROM test.table_partition_range PARTITION (p20240610, p20240611);
SELECT * FROM test.table_partition_range PARTITION (p20240610, p20240611) WHERE partition_value IN  ('2024-05-20', '2024-06-11');