env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
Jdbc {
url="********************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
query="select * from emp_104"
"fetch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"parallelism"=1
}

}
transform {
}
sink {
Doris {
fenodes="192.168.90.233:8030"
query-port="9030"
username="root"
password="Cdyanfa_123456"
database="test"
table="emp_20250709"
table.identifier="test.emp_20250709"
sink.enable-2pc="false"
doris.batch.size=1024
data_save_mode="APPEND_DATA"
doris.config={
format=json
read_json_by_line=true
}
}

}